# Quick Test Guide

## 🚀 Để test Hibernate nhanh:

### 1. Test Compilation
```java
// Chạy trong IDE:
Utils.CompilationTest.main()
```

### 2. Test Basic Entities
```java
// Chạy trong IDE:
Utils.BasicHibernateTest.main()
```

### 3. Test Hibernate (nếu database sẵn sàng)
```java
// Chạy trong IDE:
Utils.HibernateTestSimple.main()
```

## 🔧 Nếu có lỗi:

### Lỗi compilation:
- Rebuild project trong IDE
- Kiểm tra dependencies trong pom.xml

### Lỗi database:
- Khởi động MySQL server
- Tạo database "internet"
- Kiểm tra username/password trong hibernate.cfg.xml

### Lỗi Hibernate:
- Kiểm tra hibernate.cfg.xml
- Xem log trong console

## 📁 Files quan trọng:
- `pom.xml` - Dependencies
- `hibernate.cfg.xml` - Database config
- `DTO/Account.java` - Account entity
- `DTO/Computer.java` - Computer entity
- `Repository/` - Repository classes
- `Utils/HibernateUtil.java` - Hibernate utility

## ✅ Nếu test pass:
Hibernate đã sẵn sàng sử dụng!

## ❌ Nếu test fail:
Vẫn có thể dùng JDBC như cũ.
