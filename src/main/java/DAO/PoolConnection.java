package DAO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;

public class PoolConnection {
    // Thông tin kết nối
    private static final String SERVER = "localhost";
    private static final int PORT = 3306;
    private static final String DATABASE_NAME = "internet";
    private static final String USER_NAME = "root";
    private static final String PASSWORD = "1234";
    private static final int MAX_CONNECTION = 10;

    private final List<Connection> connections = new LinkedList<>();

    /**
     * Tạo mới một kết nối MySQL
     */
    private Connection createConnection() {
        try {
            // Nạp driver MySQL
            Class.forName("com.mysql.cj.jdbc.Driver");

            // URL chuẩn MySQL: disable SSL, set múi giờ
            String url = String.format(
                    "*****************************************************",
                    SERVER, PORT, DATABASE_NAME
            );
            return DriverManager.getConnection(url, USER_NAME, PASSWORD);
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL Driver not found!");
            e.printStackTrace();
        } catch (SQLException e) {
            System.err.println("Failed to create MySQL connection:");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Lấy một connection từ pool
     */
    public synchronized Connection getConnection() throws SQLException {
        if (!connections.isEmpty()) {
            return connections.remove(0);
        }
        return createConnection();
    }

    /**
     * Trả connection về pool (hoặc đóng nếu đầy)
     */
    public synchronized void releaseConnection(Connection connection) throws SQLException {
        if (connection == null) return;
        if (connections.size() < MAX_CONNECTION) {
            connections.add(connection);
        } else {
            connection.close();
        }
    }

    /**
     * Đóng tất cả connection khi shutdown
     */
    public void closeAllConnections() throws SQLException {
        for (Connection conn : connections) {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        }
        connections.clear();
    }
}
