package DAO;

import java.lang.reflect.Field;
import java.sql.*;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

public class DBHelper {
    private static DBHelper instance;

    // Lấy instance (singleton), tự tái khởi tạo nếu connection đã đóng
    private static DBHelper getInstance() throws SQLException {
        if (instance == null || instance.connection.isClosed()) {
            instance = new DBHelper();
        }
        return instance;
    }

    /**
     * Trả về Connection để DAO sử dụng
     */
    public static Connection getConnection() throws SQLException {
        return getInstance().connection;
    }

    // Thông số kết nối MySQL (có thể tách ra file properties)
    private static final String SERVER       = "localhost";
    private static final String PORT         = "3306";
    private static final String DATABASE_NAME= "internet";
    private static final String USER_NAME    = "root";
    private static final String PASSWORD     = "1234";

    private Connection connection;

    /**
     * Constructor private: nạp driver và tạo connection mới
     */
    private DBHelper() throws SQLException {
        try {
            // Load driver MySQL
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL JDBC Driver not found", e);
        }

        // Chuỗi kết nối MySQL
        String url = String.format(
                "*****************************************************",
                SERVER, PORT, DATABASE_NAME
        );
        this.connection = DriverManager.getConnection(url, USER_NAME, PASSWORD);
    }


     // Trả về chuỗi kết nối

    public static String getConnectionString() {
        return String.format(
                "*****************************************************",
                SERVER, PORT, DATABASE_NAME
        );
    }

    /**
     * Giữ nguyên phần chuyển ResultSet thành List<T>
     */
    public static <T> List<T> toList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        Field[] fields = clazz.getDeclaredFields();
        List<T> list = new ArrayList<>();

        // Get column names from ResultSet metadata
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (resultSet.next()) {
            try {
                T t = clazz.getConstructor().newInstance();
                for (Field field : fields) {
                    if (field.getName().equals("serialVersionUID")) {
                        continue;
                    }
                    try {
                        // Try to get value by field name first
                        Object value = null;
                        String fieldName = field.getName();

                        // Check if column exists in ResultSet
                        boolean columnExists = false;
                        for (int i = 1; i <= columnCount; i++) {
                            if (metaData.getColumnName(i).equalsIgnoreCase(fieldName)) {
                                columnExists = true;
                                break;
                            }
                        }

                        if (columnExists) {
                            value = resultSet.getObject(fieldName);
                        }

                        if (value == null) continue;

                        field.setAccessible(true);

                        // Handle Date field conversions first
                        if (field.getType() == java.util.Date.class) {
                            if (value instanceof java.time.LocalDateTime) {
                                // Convert LocalDateTime to java.util.Date
                                java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) value;
                                java.time.Instant instant = localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant();
                                field.set(t, java.util.Date.from(instant));
                            } else if (value instanceof java.time.LocalDate) {
                                // Convert LocalDate to java.util.Date
                                java.time.LocalDate localDate = (java.time.LocalDate) value;
                                java.time.Instant instant = localDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant();
                                field.set(t, java.util.Date.from(instant));
                            } else if (value instanceof java.sql.Timestamp) {
                                // Convert SQL Timestamp to java.util.Date
                                field.set(t, new java.util.Date(((java.sql.Timestamp) value).getTime()));
                            } else if (value instanceof java.sql.Date) {
                                // Convert SQL Date to java.util.Date
                                field.set(t, new java.util.Date(((java.sql.Date) value).getTime()));
                            } else {
                                // Try direct assignment for other Date types
                                field.set(t, value);
                            }
                        } else if (field.getType().isEnum()) {
                            int ordinal = ((Number) value).intValue();
                            Object enumVal = field.getType().getEnumConstants()[ordinal];
                            field.set(t, enumVal);
                        } else {
                            field.set(t, value);
                        }
                    } catch (Exception ex) {
                        // Log the error for debugging
                        System.err.println("Error mapping field " + field.getName() + " in class " + clazz.getName() + ": " + ex.getMessage());
                    }
                }
                list.add(t);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new SQLException("Failed to map ResultSet to " + clazz.getName(), ex);
            }
        }
        resultSet.close();
        return list;
    }
}

