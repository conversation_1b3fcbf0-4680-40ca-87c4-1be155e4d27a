/*
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>er on Fri Apr 07 13:10:50 ICT 2023
 */

package GUI.Components;

import lombok.Getter;
import lombok.Setter;
import DTO.Message;

import java.awt.*;
import java.util.Date;
import java.util.List;
import javax.swing.*;
import javax.swing.border.*;

/**
 * <AUTHOR>
 */
public class ChatGUI extends J<PERSON>rame {

    @Setter
    @Getter
    private List<Message> messages;
    private final Message.FROM sender;

    public  interface OnSendListener {
        void onSend(String content);
    }

    private OnSendListener onSendListener;

    public ChatGUI(List<Message> messages, Message.FROM sender) {
        this.messages = messages;
        this.sender = sender;
        initComponents();
        reDesign();
        reloadMessageHistory();
        initEvent();
        setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);

    }


    private void reDesign() {
        // Modern chat interface design
        setTitle("💬 Chat - Cyber Cafe Management");
        setSize(450, 600);

        // Main window styling
        getContentPane().setBackground(new Color(0xf8f9fa));

        // Chat area styling (panel2)
        panel2.setBackground(new Color(0xffffff));
        panel2.setBorder(BorderFactory.createCompoundBorder(
            new EmptyBorder(10, 10, 5, 10),
            BorderFactory.createLineBorder(new Color(0xe9ecef), 1, true)
        ));

        // Chat text area styling
        chattextArea2.setBackground(new Color(0xffffff));
        chattextArea2.setForeground(new Color(0x212529));
        chattextArea2.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        chattextArea2.setLineWrap(true);
        chattextArea2.setWrapStyleWord(true);
        chattextArea2.setMargin(new Insets(10, 10, 10, 10));

        // Scroll pane styling for chat area
        scrollPane2.setBorder(null);
        scrollPane2.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane2.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);

        // Input panel styling (panel1)
        panel1.setBackground(new Color(0xffffff));
        panel1.setBorder(BorderFactory.createCompoundBorder(
            new EmptyBorder(5, 10, 10, 10),
            BorderFactory.createLineBorder(new Color(0xe9ecef), 1, true)
        ));

        // Input text area styling
        inputTextArea.setBackground(new Color(0xf8f9fa));
        inputTextArea.setForeground(new Color(0x212529));
        inputTextArea.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputTextArea.setLineWrap(true);
        inputTextArea.setWrapStyleWord(true);
        inputTextArea.setMargin(new Insets(8, 8, 8, 8));
        inputTextArea.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1, true),
            new EmptyBorder(5, 5, 5, 5)
        ));

        // Scroll pane styling for input area
        scrollPane1.setBorder(null);
        scrollPane1.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane1.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane1.setPreferredSize(new Dimension(300, 60));

        // Send button styling
        button1.setText("📤 Gửi");
        button1.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button1.setBackground(new Color(0x007bff));
        button1.setForeground(Color.WHITE);
        button1.setFocusPainted(false);
        button1.setBorderPainted(false);
        button1.setPreferredSize(new Dimension(80, 60));
        button1.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Button hover effect
        button1.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button1.setBackground(new Color(0x0056b3));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button1.setBackground(new Color(0x007bff));
            }
        });
    }

    private void initEvent() {
        button1.addActionListener(e -> {
            if (inputTextArea.getText().trim().equals("")) {
                JOptionPane.showMessageDialog(this, "Không được để trống tin nhắn");
                return;
            }
            if (onSendListener != null) {
                onSendListener.onSend(inputTextArea.getText());
            }
            var now = new Date();
            var time = String .format("%02d:%02d:%02d", now.getHours(), now.getMinutes(), now.getSeconds());
            chattextArea2.append(String.format("Bạn ( lúc%s): %s\r\n", time, inputTextArea.getText()));
            inputTextArea.setText("");
        });
    }

    public void setOnSendListener(OnSendListener onSendListener) {
        this.onSendListener = onSendListener;
    }

    public void reloadMessageHistory() {
        chattextArea2.setText("");
        for (Message message : messages) {
            var time = message.getCreatedAt();
            //format HH:mm:ss
            String timeString = String.format("%02d:%02d:%02d", time.getHours(), time.getMinutes(), time.getSeconds());
            if (this.sender == message.getFromType()) {

                chattextArea2.append(String.format("Bạn ( lúc%s): %s\r\n", timeString, message.getContent()));
            } else {
                if (message.getFromType() == Message.FROM.SERVER) {
                    chattextArea2.append(String.format("Máy chủ (lúc %s): %s\r\n", timeString, message.getContent()));
                } else {
                    chattextArea2.append(String.format("Máy khách %s (%s): %s\r\n",message.getSession().getComputerID(), timeString, message.getContent()));
                }
            }
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        panel1 = new JPanel();
        button1 = new JButton();
        scrollPane1 = new JScrollPane();
        inputTextArea = new JTextArea();
        panel2 = new JPanel();
        scrollPane2 = new JScrollPane();
        chattextArea2 = new JTextArea();

        //======== this ========
        setPreferredSize(new Dimension(400, 500));
        var contentPane = getContentPane();
        contentPane.setLayout(new BorderLayout());

        //======== panel1 ========
        {
            panel1.setBorder(new EmptyBorder(5, 5, 5, 5));
            panel1.setLayout(new BorderLayout(5, 5));

            //---- button1 ----
            button1.setText("G\u1eedi");
            button1.setFont(new Font("Nunito", Font.BOLD, 14));
            panel1.add(button1, BorderLayout.EAST);

            //======== scrollPane1 ========
            {

                //---- inputTextArea ----
                inputTextArea.setColumns(1);
                inputTextArea.setPreferredSize(new Dimension(32, 32));
                inputTextArea.setFont(new Font("Nunito", Font.PLAIN, 14));
                scrollPane1.setViewportView(inputTextArea);
            }
            panel1.add(scrollPane1, BorderLayout.CENTER);
        }
        contentPane.add(panel1, BorderLayout.SOUTH);

        //======== panel2 ========
        {
            panel2.setBorder(new EmptyBorder(5, 5, 5, 5));
            panel2.setLayout(new BorderLayout());

            //======== scrollPane2 ========
            {

                //---- chattextArea2 ----
                chattextArea2.setEditable(false);
                chattextArea2.setFont(new Font("Nunito", Font.PLAIN, 14));
                scrollPane2.setViewportView(chattextArea2);
            }
            panel2.add(scrollPane2, BorderLayout.CENTER);
        }
        contentPane.add(panel2, BorderLayout.CENTER);
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JPanel panel1;
    private JButton button1;
    private JScrollPane scrollPane1;
    private JTextArea inputTextArea;
    private JPanel panel2;
    private JScrollPane scrollPane2;
    private JTextArea chattextArea2;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on


}
