package GUI.Components.TopNavBar;

import Utils.Fonts;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.CompoundBorder;
import javax.swing.border.MatteBorder;
import java.awt.*;

public class TopNavItem extends JButton {
    private String key;
    private JPanel contentPanel;
    private boolean selected = false;
    private String currentChildKey = null; // Track which child is currently selected
    
    public TopNavItem() {
        initStyle();
    }
    
    private void initStyle() {
        setFont(Fonts.getFont(Font.PLAIN, 14));
        setBorder(new EmptyBorder(12, 20, 12, 20));
        setFocusPainted(false);
        setContentAreaFilled(false);
        setOpaque(true);
        setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Default colors
        setBackground(Color.WHITE);
        setForeground(new Color(0x4a5568));
        
        // Hover effect
        addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                if (!selected) {
                    setBackground(new Color(0xf7fafc));
                    setForeground(new Color(0x2d3748));
                }
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                if (!selected) {
                    setBackground(Color.WHITE);
                    setForeground(new Color(0x4a5568));
                }
            }
        });
    }
    
    public void setSelected(boolean selected) {
        this.selected = selected;
        if (selected) {
            // Active state: Blue background with white text and bottom border
            setBackground(new Color(0x3182ce));
            setForeground(Color.WHITE);

            // Create compound border: padding + bottom border
            EmptyBorder padding = new EmptyBorder(12, 20, 8, 20);
            MatteBorder bottomBorder = new MatteBorder(0, 0, 4, 0, new Color(0x2c5aa0));
            setBorder(new CompoundBorder(bottomBorder, padding));
        } else {
            // Default state: White background with gray text
            setBackground(Color.WHITE);
            setForeground(new Color(0x4a5568));
            setBorder(new EmptyBorder(12, 20, 12, 20)); // Normal border
        }
        repaint();
    }
    
    public boolean isSelected() {
        return selected;
    }
    
    public String getKey() {
        return key;
    }
    
    public void setKey(String key) {
        this.key = key;
    }
    
    public JPanel getContentPanel() {
        return contentPanel;
    }
    
    public void setContentPanel(JPanel contentPanel) {
        this.contentPanel = contentPanel;
    }
    
    public String getCurrentChildKey() {
        return currentChildKey;
    }

    public void setCurrentChildKey(String currentChildKey) {
        this.currentChildKey = currentChildKey;
    }

    @Override
    public void setIcon(Icon icon) {
        super.setIcon(icon);
        setHorizontalTextPosition(SwingConstants.RIGHT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setIconTextGap(8);
    }
}
