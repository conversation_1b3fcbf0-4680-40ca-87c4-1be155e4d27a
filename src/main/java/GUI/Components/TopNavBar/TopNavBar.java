package GUI.Components.TopNavBar;

import Utils.Constants;
import Utils.Helper;
import com.formdev.flatlaf.extras.FlatSVGIcon;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class TopNavBar {

    private JPanel rootPanel;
    private JPanel contentPanel;
    private final List<TopNavItemElement> items = new ArrayList<>();
    
    public TopNavBar(JPanel rootPanel, JPanel contentPanel) {
        this.rootPanel = rootPanel;
        // rootPanel layout đã được set trong MainUI, không cần set lại ở đây
        this.contentPanel = contentPanel;
    }
    
    public void initComponent(List<Constants.Tab> tabs){
        System.out.println("DEBUG: TopNavBar initComponent called with " + tabs.size() + " tabs");
        tabs.forEach(tab -> {
            System.out.println("DEBUG: Creating nav item for: " + tab.getTitle());
            TopNavItem item = new TopNavItem();
            item.setKey(tab.getKey());
            item.setText(tab.getTitle());
            item.setIcon(tab.getIcon());
            item.setContentPanel(tab.getChildren() == null ? tab.getContentPanel() : null);
            
            var navItemElement = new TopNavItemElement(new ArrayList<>(), item);
            rootPanel.add(item);
            System.out.println("DEBUG: Added nav item '" + tab.getTitle() + "' to rootPanel");
            
            if (tab.getChildren() != null) {
                // Tạo dropdown menu cho các items có children
                JPopupMenu dropdownMenu = new JPopupMenu();
                dropdownMenu.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
                
                tab.getChildren().forEach(child -> {
                    JMenuItem childMenuItem = new JMenuItem();
                    childMenuItem.setText(child.getTitle());
                    childMenuItem.setIcon(child.getIcon());
                    childMenuItem.setBorder(new EmptyBorder(10, 15, 10, 15));
                    
                    childMenuItem.addActionListener(e -> {
                        // Clear selection của tất cả items
                        clearAllSelections();

                        // Set content panel
                        contentPanel.removeAll();
                        contentPanel.add(child.getContentPanel());
                        child.getContentPanel().setVisible(true);
                        contentPanel.repaint();
                        contentPanel.revalidate();

                        // Set selected state cho parent item
                        item.setSelected(true);

                        // Store current selected child info
                        item.setCurrentChildKey(child.getKey());
                    });
                    
                    dropdownMenu.add(childMenuItem);
                });
                
                // Add dropdown functionality to parent item
                item.addActionListener(e -> {
                    if (dropdownMenu.getComponentCount() > 0) {
                        dropdownMenu.show(item, 0, item.getHeight());
                    }
                });
            }
            
            items.add(navItemElement);
        });

        // Refresh panel để đảm bảo components được hiển thị
        rootPanel.revalidate();
        rootPanel.repaint();
        System.out.println("DEBUG: TopNavBar panel refreshed with " + items.size() + " items");

        initEvent();
    }
    
    public void initEvent(){
        items.forEach(item -> {
            if (item.getParent().getContentPanel() != null) {
                item.getParent().addActionListener(e -> {
                    // Clear selection của tất cả items
                    clearAllSelections();

                    // Set content cho item không có children
                    contentPanel.removeAll();
                    contentPanel.add(item.getParent().getContentPanel());
                    item.getParent().getContentPanel().setVisible(true);
                    contentPanel.repaint();
                    contentPanel.revalidate();
                    item.getParent().setSelected(true);
                });
            }
        });

        // Click first item by default
        if (!items.isEmpty() && items.get(0).getParent().getContentPanel() != null) {
            items.get(0).getParent().doClick();
        }
    }

    private void clearAllSelections() {
        items.forEach(TopNavItemElement::clearSelection);
    }
    
    public void navigateTo(String key){
        // Clear all selections first
        clearAllSelections();

        // Find and activate the target item
        boolean found = false;
        for (TopNavItemElement item : items) {
            // Check parent item
            if (item.getParent().getKey().equals(key)) {
                if (item.getParent().getContentPanel() != null) {
                    item.getParent().doClick();
                } else {
                    item.getParent().setSelected(true);
                }
                found = true;
                break;
            }

            // Check child items (for dropdown menus)
            // This would need to be implemented if we have child navigation
        }

        if (!found) {
            System.out.println("Navigation key not found: " + key);
        }
    }
}
