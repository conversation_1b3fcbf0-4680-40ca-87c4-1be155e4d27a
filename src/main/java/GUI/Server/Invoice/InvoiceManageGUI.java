package GUI.Server.Invoice;

import Utils.Helper;
import Utils.ServiceProvider;
import com.toedter.calendar.JDateChooser;
import DTO.*;
import BUS.*;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InvoiceManageGUI extends JPanel{
    JLabel actionShowInvoiceImport ;
    JLabel actionShowInvoiceExport ;
    JLabel titleContainShowListInvoice;

    JLabel titleContainAccountFilter;
    JLabel titleComputerFilter;
    JComboBox computersToFilter;
    JComboBox accountToFilter;
    JComboBox employeeToFilter;
    JPanel containShowListInvoice;
    JScrollPane listInvoiceScrollPaneExport;
    JScrollPane listInvoiceScrollPaneImport;
    JButton btnCancel;
    JButton btnSearch;
    JButton btnCreateInvoice;
    JDateChooser dateChooserFrom;
    JDateChooser dateChooserTo;
    DefaultTableModel listInvoiceModelExport;
    DefaultTableModel listInvoiceModelImport;
    InvoiceBUS invoiceBUS = ServiceProvider.getInstance().getService(InvoiceBUS.class);
    EmployeeBUS employeeService = ServiceProvider.getInstance().getService(EmployeeBUS.class);
    AccountBUS accountBUS = ServiceProvider.getInstance().getService(AccountBUS.class);
    ComputerBUS computerBUS = ServiceProvider.getInstance().getService(ComputerBUS.class);
    ProductBUS productBUS = ServiceProvider.getInstance().getService(ProductBUS.class);

    JTextField limitTotalFrom;
    JTextField limitTotalTo;
    ArrayList<ComboboxItem> listComputerComboboxItem;
    ArrayList<ComboboxItem> listAccountComboboxItem;
    ArrayList<ComboboxItem> listEmployeeComboboxItem;
    JTable listInvoiceExport;
    JTable listInvoiceImport;
    JLabel lbtotalMoneyAllInvoice;


    public InvoiceManageGUI(){
        this.setLayout(new BorderLayout());
        initManagerInvoice();
        event();
        List<Invoice> invoices = invoiceBUS.findAllByType(Invoice.InvoiceType.EXPORT);
        loadJTable(Invoice.InvoiceType.EXPORT,invoices);
    }

    private JLabel createTabLabel(String text, boolean isActive) {
        JLabel label = new JLabel(text);
        label.setFont(new Font("Segoe UI", Font.BOLD, 16));
        label.setCursor(new Cursor(Cursor.HAND_CURSOR));
        label.setForeground(Color.WHITE);
        label.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(0, 0, isActive ? 3 : 0, 0, Color.WHITE),
            BorderFactory.createEmptyBorder(10, 15, 10, 15)
        ));

        // Add hover effect
        label.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                if (!isTabActive(label)) {
                    label.setForeground(new Color(0xe9ecef));
                }
            }

            @Override
            public void mouseExited(MouseEvent e) {
                if (!isTabActive(label)) {
                    label.setForeground(Color.WHITE);
                }
            }
        });

        return label;
    }

    private boolean isTabActive(JLabel label) {
        return label.getBorder() instanceof javax.swing.border.CompoundBorder &&
               ((javax.swing.border.CompoundBorder) label.getBorder()).getOutsideBorder() instanceof javax.swing.border.MatteBorder;
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    private void setupModernTable(JTable table) {
        table.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        table.setRowHeight(35);
        table.setBackground(Color.WHITE);
        table.setForeground(new Color(0x212529));
        table.setSelectionBackground(new Color(0xe3f2fd));
        table.setSelectionForeground(new Color(0x1976d2));
        table.setGridColor(new Color(0xdee2e6));
        table.setShowGrid(true);
        table.setIntercellSpacing(new Dimension(1, 1));
        table.setRowSelectionAllowed(true);
        table.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);

        // Header styling
        table.getTableHeader().setFont(new Font("Segoe UI", Font.BOLD, 14));
        table.getTableHeader().setBackground(new Color(0x495057));
        table.getTableHeader().setForeground(Color.WHITE);
        table.getTableHeader().setPreferredSize(new Dimension(0, 45));
        table.getTableHeader().setBorder(BorderFactory.createEmptyBorder());
    }

    @Override
    public void setVisible(boolean aFlag) {
        if (aFlag){
            List<Invoice> invoices = invoiceBUS.findAllByType(Invoice.InvoiceType.EXPORT);
            loadJTable(Invoice.InvoiceType.EXPORT,invoices);
        }

    }

    //Cac phuogn thuc duoc dung trong code
    public static InvoiceManageGUI getInstance(){
        return new InvoiceManageGUI();
    }

    public void initManagerInvoice(){
        // Modern header design
        JLabel titleManagerInvoice = new JLabel("🧾 Quản lý hóa đơn");
        titleManagerInvoice.setFont(new Font("Segoe UI", Font.BOLD, 28));
        titleManagerInvoice.setForeground(Color.WHITE);
        titleManagerInvoice.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));

        JPanel containTitleManagerInvoice = new JPanel(new BorderLayout());
        containTitleManagerInvoice.setPreferredSize(new Dimension(1200, 80));
        containTitleManagerInvoice.setBackground(new Color(0x2c3e50));
        containTitleManagerInvoice.add(titleManagerInvoice, BorderLayout.WEST);


        // Modern tab navigation
        actionShowInvoiceExport = createTabLabel("🛒 Hóa đơn bán", true);
        actionShowInvoiceImport = createTabLabel("📦 Hóa đơn nhập", false);

        JPanel containShowInvoiceAction = new JPanel(new FlowLayout(FlowLayout.LEFT, 20, 10));
        containShowInvoiceAction.setBackground(new Color(0x2c3e50));
        containShowInvoiceAction.add(actionShowInvoiceExport);
        containShowInvoiceAction.add(actionShowInvoiceImport);



        // Modern create button
        btnCreateInvoice = createModernButton("➕ Tạo mới", new Color(0x28a745), new Color(0x218838));

        JPanel containCreateNewInvoiceAction = new JPanel(new FlowLayout(FlowLayout.RIGHT, 30, 10));
        containCreateNewInvoiceAction.setBackground(new Color(0x2c3e50));
        containCreateNewInvoiceAction.add(btnCreateInvoice);


        JPanel containActionInHeader = new JPanel(new BorderLayout());
        containActionInHeader.setPreferredSize(new Dimension(1200, 60));
        containActionInHeader.setBackground(new Color(0x2c3e50));
        containActionInHeader.add(containShowInvoiceAction, BorderLayout.WEST);
        containActionInHeader.add(containCreateNewInvoiceAction, BorderLayout.EAST);

        JPanel managerInvoiceHeader = new JPanel(new BorderLayout());
        managerInvoiceHeader.add(containTitleManagerInvoice, BorderLayout.NORTH);
        managerInvoiceHeader.add(containActionInHeader, BorderLayout.SOUTH);


        // Modern filter panel
        JLabel titleFilter = new JLabel("🔍 Bộ lọc tìm kiếm");
        titleFilter.setFont(new Font("Segoe UI", Font.BOLD, 18));
        titleFilter.setForeground(new Color(0x495057));
        titleFilter.setBorder(BorderFactory.createEmptyBorder(15, 0, 15, 0));

        JPanel containTitleFilter = new JPanel(new FlowLayout(FlowLayout.CENTER));
        containTitleFilter.setBackground(Color.WHITE);
        containTitleFilter.add(titleFilter);


        // Modern date filter
        JLabel limitDateFrom = new JLabel("📅 Từ ngày:");
        limitDateFrom.setFont(new Font("Segoe UI", Font.BOLD, 13));
        limitDateFrom.setForeground(new Color(0x495057));

        dateChooserFrom = new JDateChooser();
        dateChooserFrom.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        dateChooserFrom.setDateFormatString("yyyy-MM-dd");
        dateChooserFrom.setDate(new Date());
        dateChooserFrom.setPreferredSize(new Dimension(130, 30));

        JPanel containDateFrom = new JPanel(new BorderLayout(5, 5));
        containDateFrom.setBackground(Color.WHITE);
        containDateFrom.add(limitDateFrom, BorderLayout.NORTH);
        containDateFrom.add(dateChooserFrom, BorderLayout.CENTER);

        JLabel limitDateTo = new JLabel("📅 Đến ngày:");
        limitDateTo.setFont(new Font("Segoe UI", Font.BOLD, 13));
        limitDateTo.setForeground(new Color(0x495057));

        dateChooserTo = new JDateChooser();
        dateChooserTo.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        dateChooserTo.setDateFormatString("yyyy-MM-dd");
        dateChooserTo.setDate(new Date());
        dateChooserTo.setPreferredSize(new Dimension(130, 30));

        JPanel containDateTo = new JPanel(new BorderLayout(5, 5));
        containDateTo.setBackground(Color.WHITE);
        containDateTo.add(limitDateTo, BorderLayout.NORTH);
        containDateTo.add(dateChooserTo, BorderLayout.CENTER);

        JPanel containLimitDate = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 10));
        containLimitDate.setBackground(Color.WHITE);
        containLimitDate.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            "Khoảng thời gian",
            0, 0,
            new Font("Segoe UI", Font.BOLD, 12),
            new Color(0x6c757d)
        ));
        containLimitDate.add(containDateFrom);
        containLimitDate.add(containDateTo);



        // Modern total filter
        JLabel titleLimitTotal = new JLabel("💰 Khoảng tổng tiền:");
        titleLimitTotal.setFont(new Font("Segoe UI", Font.BOLD, 13));
        titleLimitTotal.setForeground(new Color(0x495057));

        limitTotalFrom = new JTextField();
        limitTotalFrom.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        limitTotalFrom.setPreferredSize(new Dimension(80, 30));
        limitTotalFrom.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 8, 5, 8)
        ));

        limitTotalTo = new JTextField();
        limitTotalTo.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        limitTotalTo.setPreferredSize(new Dimension(80, 30));
        limitTotalTo.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 8, 5, 8)
        ));

        JPanel containLimitTotal = new JPanel(new FlowLayout(FlowLayout.CENTER, 8, 10));
        containLimitTotal.setBackground(Color.WHITE);
        containLimitTotal.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            "Khoảng giá",
            0, 0,
            new Font("Segoe UI", Font.BOLD, 12),
            new Color(0x6c757d)
        ));
        containLimitTotal.add(titleLimitTotal);
        containLimitTotal.add(limitTotalFrom);
        containLimitTotal.add(new JLabel("→"));
        containLimitTotal.add(limitTotalTo);



//        d------Choose Employee of Filter---
        ImageIcon employeesIcon = new ImageIcon("D:\\projectJava\\src\\GUI\\img\\nhanvien.png");
        Image imgEmployee = employeesIcon.getImage().getScaledInstance(25,25,Image.SCALE_SMOOTH);
        employeesIcon = new ImageIcon(imgEmployee);
        JLabel titleContainEmployeeFilter = new JLabel("Nhân viên ",employeesIcon,JLabel.LEFT);
        employeeToFilter = new JComboBox();

        List<Employee> allEmployee;
        allEmployee = employeeService.findAllEmployee();
        listEmployeeComboboxItem = new ArrayList<ComboboxItem>();
        listEmployeeComboboxItem.add(new ComboboxItem());
        for(int i = 0; i < allEmployee.size();i++){
            listEmployeeComboboxItem.add(new ComboboxItem());
            listEmployeeComboboxItem.get(i).setId(allEmployee.get(i).getId());
            listEmployeeComboboxItem.get(i).setValue(allEmployee.get(i).getName());
            employeeToFilter.addItem(listEmployeeComboboxItem.get(i).getValue());
        }

        employeeToFilter.setPreferredSize(new Dimension(140,32));
        employeeToFilter.setSelectedItem(null);
        JPanel containEmployeeFilter = new JPanel(new FlowLayout());
        containEmployeeFilter.add(titleContainEmployeeFilter);
        containEmployeeFilter.add(new JLabel("    "));
        containEmployeeFilter.add(employeeToFilter);


//        e------choose Account of Filter---
        ImageIcon userIcon = new ImageIcon("D:\\projectJava\\src\\GUI\\img\\user.png");
        Image imgUser = userIcon.getImage().getScaledInstance(19,19,Image.SCALE_SMOOTH);
        userIcon = new ImageIcon(imgUser);
        titleContainAccountFilter = new JLabel("Tên tài khoản ",userIcon,JLabel.LEFT);
        accountToFilter = new JComboBox();
        List<Account> allAccount;
        try {
            allAccount = accountBUS.getAllAccounts();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        listAccountComboboxItem = new ArrayList<ComboboxItem>();
        listAccountComboboxItem.add(new ComboboxItem());
        listAccountComboboxItem.get(0).setId(-1);
        listAccountComboboxItem.get(0).setValue("Khách vãn lai");
        accountToFilter.addItem(listAccountComboboxItem.get(0).getValue());
        for(int i = 0; i < allAccount.size();i++){
            listAccountComboboxItem.add(new ComboboxItem());
            listAccountComboboxItem.get(i+1).setId(allAccount.get(i).getId());
            listAccountComboboxItem.get(i+1).setValue(allAccount.get(i).getUsername());
            accountToFilter.addItem(listAccountComboboxItem.get(i+1).getValue());
        }

        accountToFilter.setSelectedItem(null);
        accountToFilter.setPreferredSize(new Dimension(120,32));
        JPanel containAccountFilter = new JPanel(new FlowLayout());
        containAccountFilter.add(titleContainAccountFilter);
        containAccountFilter.add(new JLabel(""));
        containAccountFilter.add(accountToFilter);


//        f----choose Computer of FIlter
        ImageIcon computerIcon = new ImageIcon("D:\\projectJava\\src\\GUI\\img\\monitor.png");
        Image imgComputerIcon = computerIcon.getImage();
        imgComputerIcon = imgComputerIcon.getScaledInstance(20,20,Image.SCALE_SMOOTH);
        computerIcon = new ImageIcon(imgComputerIcon);
        titleComputerFilter = new JLabel("Chọn máy",computerIcon,JLabel.CENTER);
        titleComputerFilter.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(0,0,0,1)),
                BorderFactory.createEmptyBorder(0,0,0,24)
        ));
        computersToFilter = new JComboBox();
        computersToFilter.setPreferredSize(new Dimension(120,32));
        List<Computer> allComputer;
        try {
            allComputer = computerBUS.getAllComputers();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        listComputerComboboxItem = new ArrayList<>(allComputer.size());
        for(int i = 0; i < allComputer.size();i++){
            listComputerComboboxItem.add(new ComboboxItem());
            listComputerComboboxItem.get(i).setId(allComputer.get(i).getId());
            listComputerComboboxItem.get(i).setValue(allComputer.get(i).getName());
            computersToFilter.addItem(listComputerComboboxItem.get(i).getValue());
        }

        computersToFilter.setSelectedItem(null);
        JPanel containComputerFilter = new JPanel(new FlowLayout(FlowLayout.LEFT));
        containComputerFilter.setPreferredSize(new Dimension(245,40));
        containComputerFilter.add(titleComputerFilter);
        containComputerFilter.add(computersToFilter);


        // Modern filter action buttons
        btnCancel = createModernButton("🔄 Làm mới", new Color(0x6c757d), new Color(0x545b62));
        btnSearch = createModernButton("🔍 Tìm kiếm", new Color(0x007bff), new Color(0x0056b3));

        JPanel containActionInFilter = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 15));
        containActionInFilter.setBackground(Color.WHITE);
        containActionInFilter.add(btnCancel);
        containActionInFilter.add(btnSearch);


        // Modern filter panel layout
        JPanel managerInvoiceFilter = new JPanel();
        managerInvoiceFilter.setLayout(new BoxLayout(managerInvoiceFilter, BoxLayout.Y_AXIS));
        managerInvoiceFilter.setPreferredSize(new Dimension(380, 500));
        managerInvoiceFilter.setBackground(Color.WHITE);
        managerInvoiceFilter.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));

        managerInvoiceFilter.add(containTitleFilter);
        managerInvoiceFilter.add(Box.createVerticalStrut(10));
        managerInvoiceFilter.add(containLimitDate);
        managerInvoiceFilter.add(Box.createVerticalStrut(10));
        managerInvoiceFilter.add(containLimitTotal);
        managerInvoiceFilter.add(Box.createVerticalStrut(10));
        managerInvoiceFilter.add(containComputerFilter);
        managerInvoiceFilter.add(Box.createVerticalStrut(10));
        managerInvoiceFilter.add(containAccountFilter);
        managerInvoiceFilter.add(Box.createVerticalStrut(10));
        managerInvoiceFilter.add(containEmployeeFilter);
        managerInvoiceFilter.add(Box.createVerticalStrut(15));
        managerInvoiceFilter.add(containActionInFilter);
        managerInvoiceFilter.add(Box.createVerticalGlue());

//        1--------Body Filter for search end------



//        2--------Body show list invoice-start-----

        // Modern export invoice table
        listInvoiceModelExport = new DefaultTableModel() {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        String[] columnsInvoiceExport = {"ID", "👤 Tài khoản", "💻 Máy", "📅 Ngày tạo", "👨‍💼 Nhân viên", "💳 Thanh toán", "📊 Trạng thái", "💰 Tổng tiền"};
        listInvoiceModelExport.setColumnIdentifiers(columnsInvoiceExport);

        listInvoiceExport = new JTable(listInvoiceModelExport);
        setupModernTable(listInvoiceExport);
        listInvoiceExport.setComponentPopupMenu(operationForInvoice(listInvoiceExport, listInvoiceModelExport));

        // Column widths for export table
        listInvoiceExport.getColumnModel().getColumn(0).setPreferredWidth(50);
        listInvoiceExport.getColumnModel().getColumn(1).setPreferredWidth(120);
        listInvoiceExport.getColumnModel().getColumn(2).setPreferredWidth(80);
        listInvoiceExport.getColumnModel().getColumn(3).setPreferredWidth(120);
        listInvoiceExport.getColumnModel().getColumn(4).setPreferredWidth(120);
        listInvoiceExport.getColumnModel().getColumn(5).setPreferredWidth(100);
        listInvoiceExport.getColumnModel().getColumn(6).setPreferredWidth(100);
        listInvoiceExport.getColumnModel().getColumn(7).setPreferredWidth(120);



        // Modern import invoice table
        listInvoiceModelImport = new DefaultTableModel() {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        String[] columnsInvoiceImport = {"ID", "📅 Ngày tạo", "👨‍💼 Nhân viên", "💳 Thanh toán", "📊 Trạng thái", "💰 Tổng tiền"};
        listInvoiceModelImport.setColumnIdentifiers(columnsInvoiceImport);

        listInvoiceImport = new JTable(listInvoiceModelImport);
        setupModernTable(listInvoiceImport);
        listInvoiceImport.setComponentPopupMenu(operationForInvoice(listInvoiceImport, listInvoiceModelImport));



        // Modern scroll panes
        listInvoiceScrollPaneExport = new JScrollPane(listInvoiceExport);
        listInvoiceScrollPaneExport.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        listInvoiceScrollPaneExport.getViewport().setBackground(Color.WHITE);

        listInvoiceScrollPaneImport = new JScrollPane(listInvoiceImport);
        listInvoiceScrollPaneImport.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        listInvoiceScrollPaneImport.getViewport().setBackground(Color.WHITE);

        // Modern table title
        titleContainShowListInvoice = new JLabel("🛒 Danh sách hóa đơn bán", JLabel.CENTER);
        titleContainShowListInvoice.setFont(new Font("Segoe UI", Font.BOLD, 20));
        titleContainShowListInvoice.setForeground(new Color(0x495057));
        titleContainShowListInvoice.setBorder(BorderFactory.createEmptyBorder(15, 0, 15, 0));

        // Modern total money display
        lbtotalMoneyAllInvoice = new JLabel("💰 Tổng tiền: 0.0 VNĐ");
        lbtotalMoneyAllInvoice.setFont(new Font("Segoe UI", Font.BOLD, 16));
        lbtotalMoneyAllInvoice.setForeground(new Color(0x28a745));

        JPanel bodyFooter = new JPanel(new FlowLayout(FlowLayout.RIGHT, 20, 10));
        bodyFooter.setBackground(Color.WHITE);
        bodyFooter.add(lbtotalMoneyAllInvoice);

        // Modern table container
        containShowListInvoice = new JPanel(new BorderLayout());
        containShowListInvoice.setBackground(Color.WHITE);
        containShowListInvoice.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));
        containShowListInvoice.add(titleContainShowListInvoice, BorderLayout.NORTH);
        containShowListInvoice.add(listInvoiceScrollPaneExport, BorderLayout.CENTER);
        containShowListInvoice.add(bodyFooter, BorderLayout.SOUTH);


        // Modern body layout
        JPanel managerInvoiceBody = new JPanel(new BorderLayout(20, 0));
        managerInvoiceBody.setBackground(new Color(0xf8f9fa));
        managerInvoiceBody.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        managerInvoiceBody.add(managerInvoiceFilter, BorderLayout.WEST);
        managerInvoiceBody.add(containShowListInvoice, BorderLayout.CENTER);

        // Modern main container
        JPanel managerInvoiceContentPane = new JPanel(new BorderLayout());
        managerInvoiceContentPane.setBackground(new Color(0xf8f9fa));
        managerInvoiceContentPane.add(managerInvoiceHeader, BorderLayout.NORTH);
        managerInvoiceContentPane.add(managerInvoiceBody, BorderLayout.CENTER);

        this.add(managerInvoiceContentPane, BorderLayout.CENTER);
        this.setVisible(true);
    }



    //phan xu ly su kien
    public void event(){
        actionShowInvoiceImport.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                actionShowInvoiceImport.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createMatteBorder(0,0,2,0,Color.WHITE),
                        BorderFactory.createEmptyBorder(0,0,5,0)
                ));
                actionShowInvoiceExport.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(0,0,0,1)),
                        BorderFactory.createEmptyBorder(0,0,0,0)
                ));

                titleContainShowListInvoice.setText("Danh sách hóa đơn nhập");
                computersToFilter.setSelectedItem(null);
                titleComputerFilter.setEnabled(false);
                computersToFilter.setEnabled(false);
                titleContainAccountFilter.setEnabled(false);
                accountToFilter.setSelectedItem(null);
                accountToFilter.setEnabled(false);
                employeeToFilter.setSelectedItem(null);

                containShowListInvoice.remove(listInvoiceScrollPaneExport);
                containShowListInvoice.add(listInvoiceScrollPaneImport);

                List<Invoice> invoices = invoiceBUS.findAllByType(Invoice.InvoiceType.IMPORT);
                loadJTable(Invoice.InvoiceType.IMPORT,invoices);
            }
        });



        actionShowInvoiceExport.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                actionShowInvoiceExport.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createMatteBorder(0,0,2,0,Color.WHITE),
                        BorderFactory.createEmptyBorder(0,0,5,0)
                ));
                actionShowInvoiceImport.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(0,0,0,1)),
                        BorderFactory.createEmptyBorder(0,0,0,0)
                ));

                titleContainShowListInvoice.setText("Danh sách hóa đơn bán");
                computersToFilter.setEnabled(true);
                titleComputerFilter.setEnabled(true);
                titleContainAccountFilter.setEnabled(true);
                accountToFilter.setEnabled(true);
                computersToFilter.setSelectedItem(null);
                accountToFilter.setSelectedItem(null);
                employeeToFilter.setSelectedItem(null);

                containShowListInvoice.remove(listInvoiceScrollPaneImport);
                containShowListInvoice.add(listInvoiceScrollPaneExport);
                List<Invoice> invoices = invoiceBUS.findAllByType(Invoice.InvoiceType.EXPORT);
                loadJTable(Invoice.InvoiceType.EXPORT,invoices);
            }
        });





        btnCreateInvoice.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                CreateInvoiceGUI createInvoice = new CreateInvoiceGUI();
                String[] options = {"Hóa đơn bán","Hóa đơn nhập"};
                int userOption = JOptionPane.showOptionDialog(null,"Hóa đơn muốn tạo ?","Options create invoice ",JOptionPane.UNDEFINED_CONDITION,JOptionPane.QUESTION_MESSAGE,null,options,null);
                if(userOption == 0){
                    createInvoice.showDiaLog(Invoice.InvoiceType.EXPORT);
                    createInvoice.setOnsave((type,invoices)->{
                        loadJTable(type,invoices);
                    });
                }
                else if(userOption == 1){
                    createInvoice.showDiaLog(Invoice.InvoiceType.IMPORT);
                    createInvoice.setOnsave((type,invoices)->{
                        loadJTable(type,invoices);
                    });
                }
                else {
                    createInvoice.setVisible(false);
                    createInvoice.jDialog.dispose();
                }
            }
        });



        btnCancel.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dateChooserFrom.setDate(new Date());
                dateChooserTo.setDate(new Date());
                limitTotalFrom.setText("");
                limitTotalTo.setText("");
                computersToFilter.setSelectedItem(null);
                employeeToFilter.setSelectedItem(null);
                accountToFilter.setSelectedItem(null);

                Invoice.InvoiceType type = getTypeInvoice();
                List<Invoice> invoices = invoiceBUS.findAllByType(type);
                if(getTypeInvoice() == Invoice.InvoiceType.EXPORT)
                    loadJTable(Invoice.InvoiceType.EXPORT,invoices);
                else
                    loadJTable(Invoice.InvoiceType.IMPORT,invoices);
            }
        });

        btnSearch.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JTextField getDateFrom = (JTextField) dateChooserFrom.getDateEditor().getUiComponent();
                JTextField getDateTo = (JTextField) dateChooserTo.getDateEditor().getUiComponent();
                int countLossInforFilter = 0;
                if (getDateFrom.getText().equals("dd-mm-yyyy") || getDateFrom.getText().equals("")) {
                    countLossInforFilter += 1;
                }
                if (getDateTo.getText().equals("dd-mm-yyyy") || getDateTo.getText().equals("")) {
                    countLossInforFilter += 1;
                }

                if (countLossInforFilter != 0) {
                    JOptionPane.showMessageDialog(null, "Bạn phải nhập đủ thông tin tìm kiếm", "message", JOptionPane.ERROR_MESSAGE);
                    return;
                } else {
                    Invoice.InvoiceType type = getTypeInvoice();
                    InforFilter inforFilter = getInforFilter(type);//lay thong tin can search
                    List<Invoice> listInvoiceSearch;
                    if (invoiceBUS.ValidateInforFilter(inforFilter)) {

                        listInvoiceSearch = invoiceBUS.findInvoiceByInforFilter(type, inforFilter);
                        loadJTable(type, listInvoiceSearch);
                        if (listInvoiceSearch.size() == 0) {
                            JOptionPane.showMessageDialog(null, "Không có hóa đơn nào");
                        }
                    } else {
                        JOptionPane.showMessageDialog(null, "Thông tin lọc hóa đơn chưa chính xác", "message", JOptionPane.ERROR_MESSAGE);
                    }
                }
            }
        });

    }


    public InforFilter getInforFilter(Invoice.InvoiceType type){
        int accountID,computerID,employeeID;
        if(type == Invoice.InvoiceType.EXPORT){
            if(accountToFilter.getSelectedItem() == null)
                accountID = 0;
            else
                accountID = listAccountComboboxItem.get(accountToFilter.getSelectedIndex()).getId();
            if (computersToFilter.getSelectedItem() == null)
                computerID = 0;
            else
                computerID = listComputerComboboxItem.get(computersToFilter.getSelectedIndex()).getId();
        }
        else {
            accountID = computerID = 0;
        }
        if(employeeToFilter.getSelectedItem() == null)
            employeeID = 0;
        else
            employeeID = listEmployeeComboboxItem.get(employeeToFilter.getSelectedIndex()).getId();
        String strdateFrom = ((JTextField)dateChooserFrom.getDateEditor().getUiComponent()).getText();
        String strdateTo = ((JTextField)dateChooserTo.getDateEditor().getUiComponent()).getText();
        return new InforFilter(strdateFrom,strdateTo,limitTotalFrom.getText(),limitTotalTo.getText(),computerID,employeeID,accountID);
    }


    public void loadJTable(Invoice.InvoiceType type,List<Invoice> invoices){
        double totalMoneyAllInvoice = 0.0;
        if(type == Invoice.InvoiceType.EXPORT){
            listInvoiceModelExport.setRowCount(0);
            for (Invoice value : invoices) {
                totalMoneyAllInvoice += value.getTotal();
                String userNameAccount = null, computerName = null;
                String nameEmployee = employeeService.findEmployeeById(value.getCreatedBy()).getName();
                try {
                    if (value.getCreatedToAccountId() == null) {
                        userNameAccount = "Khách vãng lai";
                    } else {
                        userNameAccount = accountBUS.findById(value.getCreatedToAccountId()).getUsername();
                    }
                    computerName = computerBUS.getComputerById(value.getComputerId()).getName();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
                listInvoiceModelExport.addRow(new Object[]{value.getId(), userNameAccount, computerName, value.getCreatedAt(), nameEmployee, value.explainIsPaid(), value.getStatus(), value.getTotal()});
            }

        }
        else {
            listInvoiceModelImport.setRowCount(0);
            for (Invoice value : invoices) {
                totalMoneyAllInvoice += value.getTotal();

                // Handle null employee case
                Employee employee = employeeService.findEmployeeById(value.getCreatedBy());
                String nameEmployee = (employee != null) ? employee.getName() : "Nhân viên không tồn tại (ID: " + value.getCreatedBy() + ")";

                listInvoiceModelImport.addRow(new Object[]{value.getId(), value.getCreatedAt(), nameEmployee, value.explainIsPaid(), value.getStatus(), value.getTotal()});
            }
        }
        lbtotalMoneyAllInvoice.setText("Tổng tiền: " + Helper.formatMoney(totalMoneyAllInvoice));

    }



    public Invoice.InvoiceType getTypeInvoice(){
        if(titleContainShowListInvoice.getText().equalsIgnoreCase("danh sách hóa đơn bán")){
            return Invoice.InvoiceType.EXPORT;
        }
        return Invoice.InvoiceType.IMPORT;
    }



    public JPopupMenu operationForInvoice(JTable jtabel,DefaultTableModel model) {
        List<Invoice> invoices = invoiceBUS.findAll();
        JMenuItem delete = new JMenuItem("Xoá");
        delete.setForeground(Color.RED);
        delete.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (jtabel.getSelectedRowCount() == 1) {
                    int confirmDeleteInvoice = JOptionPane.showConfirmDialog(null, "Bạn muốn xóa hóa đơn này ?");
                    if (confirmDeleteInvoice == JOptionPane.NO_OPTION) {
                        return;
                    } else if (confirmDeleteInvoice == JOptionPane.YES_OPTION) {
                        int indexRowSelected = jtabel.getSelectedRow();
                        int idInvoiceSelected = (int) jtabel.getValueAt(indexRowSelected, 0);
                        invoiceBUS.deleteInvoice(idInvoiceSelected);
                        model.removeRow(indexRowSelected);
                        JOptionPane.showMessageDialog(null, "Xóa thành công", null, JOptionPane.INFORMATION_MESSAGE);
                    }
                } else {
                    JOptionPane.showMessageDialog(null, "Không thể xóa nhiều hóa đơn cùng lúc", "Anounce", JOptionPane.ERROR_MESSAGE);
                }
            }
        });

        JMenuItem edit = new JMenuItem("Sửa");
        edit.setFont(new Font("nunito", Font.BOLD, 16));
        edit.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                CreateInvoiceGUI createInvoiceGUI = new CreateInvoiceGUI();
                loadInvoice(createInvoiceGUI);
                createInvoiceGUI.btnAddInvoice.setVisible(false);
                createInvoiceGUI.btnSaveInvoice.setVisible(true);
            }
        });


        JMenuItem showDetailInvoice = new JMenuItem("Xem chi tiết");
        showDetailInvoice.setFont(new Font("nunito", Font.BOLD, 16));
        delete.setFont(new Font("nunito", Font.BOLD, 16));

        showDetailInvoice.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                CreateInvoiceGUI createInvoiceGUI = new CreateInvoiceGUI();
                loadInvoice(createInvoiceGUI);
                createInvoiceGUI.btnSaveInvoice.setVisible(false);
                createInvoiceGUI.btnAddInvoice.setVisible(false);
            }
        });

        JPopupMenu listOperation = new JPopupMenu();
        listOperation.add(delete);
        listOperation.add(edit);
        listOperation.add(showDetailInvoice);
        return  listOperation;
    }


    public void loadInvoice(CreateInvoiceGUI createInvoiceGUI){
//        CreateInvoiceGUI createInvoiceGUI = new CreateInvoiceGUI();
        Invoice.InvoiceType type = getTypeInvoice();
        JTable jTable;
        createInvoiceGUI.showDiaLog(type);
        //thay doi tieu de "Tao hoa don ban" thanh "Chi tiet hoa don ban"
        if(type == Invoice.InvoiceType.EXPORT){
            createInvoiceGUI.titleCreateInvoice.setText("Chi tiết hóa đơn bán");
            jTable = listInvoiceExport;
        }
        else{
            createInvoiceGUI.titleCreateInvoice.setText("Chi tiết hóa đơn nhập");
            jTable = listInvoiceImport;
        }
        //lay thong tin invoice
        //goi toi table luu du thong tin hoa don, lay id cua hoa don, goi toi ham tim hoa don bang id,
        int rowIsSelected = jTable.getSelectedRow();//lay hang duoc select
        int idInvoiceIsSelected = (int)jTable.getModel().getValueAt(rowIsSelected,0);//lay id cua hoa don
        Invoice invoiceIsSelect = invoiceBUS.findInvoiceById(idInvoiceIsSelected);//lay hoa don tu csdl len

        //lay detailInvoice cua invoice do
        InvoiceDetailBUS invoiceDetailBUS = new InvoiceDetailBUS();
        List<InvoiceDetail> listInvoiceDetail = invoiceDetailBUS.findByInvoiceId(idInvoiceIsSelected);
        if(type == Invoice.InvoiceType.EXPORT){
            for (InvoiceDetail invoiceDetail : listInvoiceDetail) {
                Product product = productBUS.findProductById(invoiceDetail.getProductId());
                createInvoiceGUI.listProductInvoiceModel.addRow(new Object[]{invoiceDetail.getProductId(), product.getName(), invoiceDetail.getQuantity(), invoiceDetail.getPrice(), invoiceDetail.getPrice() * invoiceDetail.getQuantity()});
            }

            createInvoiceGUI.listComputerID.setSelectedIndex(invoiceIsSelect.getComputerId()-1);
            if(invoiceIsSelect.getCreatedToAccountId() == null){
                createInvoiceGUI.listAccountID.setSelectedItem(0);//neu ma id cua tai khoan la null thi set mac dinh do la khach hang van lai
            }
            else{
                createInvoiceGUI.listAccountID.setSelectedIndex(invoiceIsSelect.getCreatedToAccountId());
            }
        }
        else{
            for (InvoiceDetail invoiceDetail : listInvoiceDetail) {
                Product product = productBUS.findProductById(invoiceDetail.getProductId());
                createInvoiceGUI.listProductInvoiceModel.addRow(new Object[]{product.getId(), product.getName(), invoiceDetail.getQuantity(), invoiceDetail.getPrice(), invoiceDetail.getPrice() * invoiceDetail.getQuantity()});
                createInvoiceGUI.lbTotalInvoice.setText(String.valueOf(createInvoiceGUI.caculateTotalMoney()) + "VNĐ");
            }
            createInvoiceGUI.listComputerID.setSelectedItem(null);
            createInvoiceGUI.listAccountID.setSelectedItem(null);
        }
        createInvoiceGUI.lbTotalInvoice.setText(String.valueOf(createInvoiceGUI.caculateTotalMoney()) + "VNĐ");

        //do du lieu len
        createInvoiceGUI.dateCreate.setDate(invoiceIsSelect.getCreatedAt());
        createInvoiceGUI.listEmployeeID.setSelectedIndex(invoiceIsSelect.getCreatedBy()-1);
        //neu isPaid la true tuc la hoa don da duoc thanh toan roi
        createInvoiceGUI.isPaid.setSelected(invoiceIsSelect.isPaid());
        createInvoiceGUI.status.setSelectedItem(invoiceIsSelect.getStatus());
        createInvoiceGUI.idInvoiceSelected.setText(String.valueOf(idInvoiceIsSelected));//lay id cua hoa don muon edit de luu vao lable
        createInvoiceGUI.setOnsave((a,b)->{
            loadJTable(a,b);
        });
    }

    public static void main(String[] args){
        ServiceProvider.init();
        InvoiceManageGUI quanlyhoadon = new InvoiceManageGUI();
        JFrame frame = new JFrame();
        frame.setExtendedState(JFrame.MAXIMIZED_BOTH);
        frame.setLayout(new BorderLayout());
        frame.add(quanlyhoadon,BorderLayout.CENTER);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
