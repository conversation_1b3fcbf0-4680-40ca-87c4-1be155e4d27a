/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Mar 11 17:09:21 ICT 2023
 */

package GUI.Server;

import BUS.ComputerUsageBUS;
import GUI.Blur;
import GUI.Components.SideBar.SideBar;
import Utils.Constants;
import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import lombok.Getter;
import lombok.Setter;
import DTO.Employee;

import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.sql.SQLException;
import java.util.Date;
import javax.swing.*;
import javax.swing.border.EmptyBorder;

/**
 * <AUTHOR>
 */


public class MainUI extends JFrame {
    @Getter
    @Setter
    private static Employee currentUser;
    private ComputerUsageBUS computerUsageBUS;

    public static void login(Employee currentUser) {
        MainUI.currentUser = currentUser;
        loginTime = new Date();
    }

    @Getter
    private static Date loginTime;
    public static MainUI instance;
    private Blur blur = null;
    public void setBlur(boolean b) {
        if (blur == null) {
            blur = new Blur(this);
        }
        blur.setVisible(b);
    }
    public static MainUI getInstance(boolean isNew) {
        if (isNew) {
            instance = new MainUI();
        }
        return instance;
    }
    public static MainUI getInstance() {
        if (instance == null) {
            instance = new MainUI();
        }
        return instance;
    }
    @Getter
    private SideBar sideBar;

    private MainUI() {
        computerUsageBUS = ServiceProvider.getInstance().getService(ComputerUsageBUS.class);
        initComponents();
        sideBar = new SideBar(panel3, panel2);
        sideBar.initComponent(Constants.getTabs());
        setExtendedState(JFrame.MAXIMIZED_BOTH);
        initEvent();
        // disable minimize button

    }
    private JLabel userLabel;
    @Override
    public void setVisible(boolean b) {
        super.setVisible(b);
        if (b) {
            userLabel.setText(currentUser.getName());
        }
    }
    private void initEvent() {
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                super.windowClosing(e);
                System.exit(0);
            }
        });

    }
    private void initComponents() {
        computerUsageBUS = ServiceProvider.getInstance().getService(ComputerUsageBUS.class);

        // 1) Chuẩn bị contentPane
        var contentPane = getContentPane();
        contentPane.setLayout(new BorderLayout());
        contentPane.setBackground(new Color(0xEDF2F7));

        // 2) TẠO NAVBAR Ở TOP
        JPanel navPanel = new JPanel(new BorderLayout());
        navPanel.setBackground(Color.WHITE);
        navPanel.setBorder(new EmptyBorder(5, 20, 5, 20));
        navPanel.setPreferredSize(new Dimension(0, 60));

        // 2.1) Menu ở trái
        JPanel menuPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 20, 15));
        menuPanel.setOpaque(false);
        JButton btnQuanLy = createNavButton("Quản lý");
        JButton btnThongKe = createNavButton("Thống kê");
        JButton btnCaNhan = createNavButton("Cá nhân");
        menuPanel.add(btnQuanLy);
        menuPanel.add(btnThongKe);
        menuPanel.add(btnCaNhan);
        navPanel.add(menuPanel, BorderLayout.WEST);

        // 2.2) User + Logout ở phải
        JPanel userPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 15));
        userPanel.setOpaque(false);
        JLabel xinChaoLabel = new JLabel("Xin chào:");
        xinChaoLabel.setFont(Fonts.getFont(Font.PLAIN, 18));
        userLabel = new JLabel("null");
        userLabel.setFont(Fonts.getFont(Font.BOLD, 18));
        JButton logoutButton = new JButton(Helper.getIcon("/icons/logoutser.png", 24, 24));
        logoutButton.setBorder(BorderFactory.createEmptyBorder());
        logoutButton.setContentAreaFilled(false);
        logoutButton.addActionListener(e -> doLogout());
        userPanel.add(xinChaoLabel);
        userPanel.add(userLabel);
        userPanel.add(logoutButton);
        navPanel.add(userPanel, BorderLayout.EAST);

        contentPane.add(navPanel, BorderLayout.NORTH);

        // 3) Đưa panel2 (nội dung chính) xuống CENTER
        panel2 = new JPanel(new BorderLayout());
        panel2.setBackground(new Color(0xEDF2F7));
        contentPane.add(panel2, BorderLayout.CENTER);

        // 4) Thiết lập Frame
        setExtendedState(JFrame.MAXIMIZED_BOTH);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        pack();
        setVisible(true);
    }

    // Hàm tạo nút điều hướng
    private JButton createNavButton(String text) {
        JButton b = new JButton(text);
        b.setFocusPainted(false);
        b.setBorder(BorderFactory.createEmptyBorder(5, 15, 5, 15));
        b.setFont(Fonts.getFont(Font.PLAIN, 16));
        b.setBackground(new Color(0xEDF2F7));
        b.addActionListener(e -> {
            // TODO: chuyển nội dung bên panel2 theo tab được click
            System.out.println("Chuyển sang tab: " + text);
        });
        return b;
    }

    // Hàm xử lý logout
    private void doLogout() {
        try {
            computerUsageBUS.createForEmployee(loginTime, new Date(), currentUser.getAccountID());
        } catch (SQLException ex) {
            ex.printStackTrace();
        }
        currentUser = null;
        loginTime = null;
        dispose();
        new LoginGUI();
    }
