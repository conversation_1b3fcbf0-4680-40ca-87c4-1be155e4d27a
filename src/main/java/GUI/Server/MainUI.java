/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Mar 11 17:09:21 ICT 2023
 */

package GUI.Server;

import BUS.ComputerUsageBUS;
import GUI.Blur;
import GUI.Components.TopNavBar.TopNavBar;
import Utils.Constants;
import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import lombok.Getter;
import lombok.Setter;
import DTO.Employee;

import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.sql.SQLException;
import java.util.Date;
import javax.swing.*;
import javax.swing.border.EmptyBorder;

/**
 * <AUTHOR>
 */


public class MainUI extends J<PERSON>rame {
@Getter
@Setter
private static Employee currentUser;
private ComputerUsageBUS computerUsageBUS;

public static void login(Employee currentUser) {
    MainUI.currentUser = currentUser;
    loginTime = new Date();
}

@Getter
private static Date loginTime;
public static MainUI instance;
private Blur blur = null;
public void setBlur(boolean b) {
    if (blur == null) {
        blur = new Blur(this);
    }
    blur.setVisible(b);
}
public static MainUI getInstance(boolean isNew) {
    if (isNew) {
        instance = new MainUI();
    }
    return instance;
}
public static MainUI getInstance() {
    if (instance == null) {
        instance = new MainUI();
    }
    return instance;
}
@Getter
private TopNavBar topNavBar;

private JPanel centerNavPanel; // Add field for center navigation panel

private MainUI() {
    computerUsageBUS = ServiceProvider.getInstance().getService(ComputerUsageBUS.class);
    initComponents();
    // TopNavBar phải được tạo sau khi centerNavPanel đã được khởi tạo
    topNavBar = new TopNavBar(centerNavPanel, panel2);
    topNavBar.initComponent(Constants.getTabs());
    setExtendedState(JFrame.MAXIMIZED_BOTH);
    initEvent();
    // disable minimize button

}
private JLabel userLabel;
@Override
public void setVisible(boolean b) {
    super.setVisible(b);
    if (b) {
        userLabel.setText(currentUser.getName());
    }
}
private void initEvent() {
    addWindowListener(new WindowAdapter() {
        @Override
        public void windowClosing(WindowEvent e) {
            super.windowClosing(e);
            System.exit(0);
        }
    });

}

private void initComponents() {
    panel2 = new JPanel();
    panel3 = new JPanel();
    var panel4 = new JPanel();
    panel4.setLayout(new BorderLayout());
    panel4.setBorder(new EmptyBorder(20, 20, 20, 20));
    panel4.setBackground(new Color(0xedf2f7));

    var contentPane = getContentPane();
    contentPane.setBackground(new Color(0xedf2f7));
    contentPane.setLayout(new BorderLayout());

    // Main content panel
    panel2.setBackground(new Color(0xedf2f7));
    panel2.setLayout(new BorderLayout());

    // Top navigation panel
    panel3.setMinimumSize(new Dimension(32, 60));
    panel3.setMaximumSize(new Dimension(32767, 60));
    panel3.setPreferredSize(new Dimension(32, 60));
    panel3.setBackground(Color.white);
    panel3.setLayout(new BorderLayout());
    panel3.setBorder(new EmptyBorder(5, 10, 5, 10));

    // Add panels to content pane
    contentPane.add(panel3, BorderLayout.NORTH);  // Top navigation
    contentPane.add(panel4, BorderLayout.CENTER); // Main content area
    panel4.add(panel2, BorderLayout.CENTER);

    setLocationRelativeTo(getOwner());
    // Create user panel for top navigation
    var userPanel = new JPanel();
    userPanel.setLayout(new FlowLayout(FlowLayout.RIGHT));
    userPanel.setPreferredSize(new Dimension(400, 50));
    userPanel.setBackground(Color.white);

    var xinChaoLabel = new JLabel("Xin chào: ");
    xinChaoLabel.setFont(Fonts.getFont(Font.PLAIN, 14));
    xinChaoLabel.setBorder(new EmptyBorder(0, 10, 0, 5));

    userLabel = new JLabel("null");
    userLabel.setFont(Fonts.getFont(Font.BOLD, 14));
    userLabel.setBorder(new EmptyBorder(0, 0, 0, 15));

    JButton logoutButton = new JButton();
    logoutButton.setIcon(Helper.getIcon("/icons/logoutser.png", 24, 24));
    logoutButton.setBackground(new Color(0x0bc5ea));
    logoutButton.setPreferredSize(new Dimension(40, 40));
    logoutButton.setBorder(new EmptyBorder(5, 5, 5, 5));
    logoutButton.setFocusPainted(false);
    logoutButton.setCursor(new Cursor(Cursor.HAND_CURSOR));

    userPanel.add(xinChaoLabel);
    userPanel.add(userLabel);
    userPanel.add(logoutButton);

    logoutButton.addActionListener(e -> {
        setVisible(false);
        try {
            computerUsageBUS.createForEmployee(
                    loginTime,
                    new Date(),
                    currentUser.getAccountID()
            );
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        currentUser = null;
        loginTime = null;
        new LoginGUI();
    });
    // Create center panel for navigation items
    centerNavPanel = new JPanel();
    centerNavPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));
    centerNavPanel.setBackground(Color.white);

    // Add panels to navigation bar
    panel3.add(centerNavPanel, BorderLayout.CENTER); // Navigation items in center
    panel3.add(userPanel, BorderLayout.EAST);        // User panel on the right
}

private JPanel panel2;
private JPanel panel3;


}
