package GUI.Server.Employee;

import BUS.*;
import DTO.Account;
import DTO.Employee;
import GUI.Server.MainUI;

import BUS.EmployeeBUS;
import Utils.ServiceProvider;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ItemEvent;
import java.awt.event.MouseEvent;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ManagerEmployee extends JPanel {
    private List<Employee> list;
    private EmployeeBUS employeeService;
    private AccountBUS accountBUS;

    private Employee employee = Employee.builder().id(0).name("").accountID(null).salaryPerHour(0).phoneNumber("").address("").createdAt(new Date()).deletedAt(null).build();
    private JPanel managerEmployeeContentPane;
    private JPanel containTitleManagerEmployee;
    private JLabel titleManagerEmployee;
    private JLabel titleContainShowListEmployee;
    private JButton btnSearch;
    private JPanel containShowListEmployee;
    private JLabel idNV;
    private JComboBox<TaiKhoanComboBoxItem> inputIdNV;

    public record TaiKhoanComboBoxItem(Integer id, String username) {
        @Override
        public String toString() {
            return username;
        }

    }

    private JPanel panelBody;
    private GridBagLayout layoutBody;
    private GridBagConstraints gbcBody;
    private JPanel containIdNV;
    private JLabel nameNV;
    private JTextField inputNameNV;
    private JPanel containNameNV;
    private JLabel luongNV;
    private JTextField inputLuongNV;
    private JPanel containLuongNV;
    private JLabel sdtNV;
    private JTextField inputSdtNV;
    private JPanel containSdtNV;
    private JLabel diachiNV;
    private JTextField inputDiachiNV;
    private JPanel containDiachiNV;
    private JButton btnAdd;
    private JButton btnEdit;
    private JButton btnDelete;
    private DefaultTableModel listEmployeeModel;
    private JTable listEmployee;
    private JScrollPane listEmployeeScrollPane;

    public ManagerEmployee() {
        this.employeeService = ServiceProvider.getInstance().getService(EmployeeBUS.class);
        this.accountBUS = ServiceProvider.getInstance().getService(AccountBUS.class);

        this.setLayout(new BorderLayout());
        try {
            this.initManagerEmployee();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }

    public void sizeInComputer(JPanel jpanel) {
        jpanel.setPreferredSize(new Dimension(1200, 650));
    }

    public void setMarginJLabel(int top, int left, int buttom, int right, JLabel jlabel) {
        jlabel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(0, 0, 0, 1)),
                BorderFactory.createEmptyBorder(top, left, buttom, right)
        ));
    }

    public void setPaddingJButton(int top, int left, int buttom, int right, JButton jbutton) {
        jbutton.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(0, 0, 0, 1)),
                BorderFactory.createEmptyBorder(top, left, buttom, right)
        ));
    }

    public void setPlaceHoder(String textPlaceHoder, JTextField jtextField) {
        jtextField.setText(textPlaceHoder);
        jtextField.setFont(new Font("nunito", Font.PLAIN, 16));
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    public void initManagerEmployee() throws SQLException {
        // Modern header design
        titleManagerEmployee = new JLabel("👥 Quản lý nhân viên");
        titleManagerEmployee.setFont(new Font("Segoe UI", Font.BOLD, 28));
        titleManagerEmployee.setForeground(Color.WHITE);
        titleManagerEmployee.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));

        containTitleManagerEmployee = new JPanel();
        containTitleManagerEmployee.setLayout(new BorderLayout());
        containTitleManagerEmployee.setPreferredSize(new Dimension(1200, 80));
        containTitleManagerEmployee.setBackground(new Color(0x2c3e50));
        containTitleManagerEmployee.add(titleManagerEmployee, BorderLayout.WEST);
        list = new ArrayList<>();
        list = employeeService.findAllEmployee();

        // Modern form design
        panelBody = new JPanel();
        panelBody.setLayout(new GridBagLayout());
        panelBody.setBackground(Color.WHITE);
        panelBody.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(new Color(0xdee2e6), 2),
                "📝 Thông tin nhân viên",
                0, 0,
                new Font("Segoe UI", Font.BOLD, 16),
                new Color(0x495057)
            ),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        gbcBody = new GridBagConstraints();
        gbcBody.insets = new Insets(10, 10, 10, 10);

        // Account ID field
        gbcBody.gridx = 0; gbcBody.gridy = 0;
        gbcBody.anchor = GridBagConstraints.WEST;
        idNV = new JLabel("🔑 Tài khoản:");
        idNV.setFont(new Font("Segoe UI", Font.BOLD, 14));
        idNV.setForeground(new Color(0x495057));
        panelBody.add(idNV, gbcBody);

        gbcBody.gridx = 1; gbcBody.fill = GridBagConstraints.HORIZONTAL;
        inputIdNV = new JComboBox<>();
        inputIdNV.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputIdNV.setPreferredSize(new Dimension(200, 35));
        inputIdNV.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));

        var accounts = accountBUS.getAllAccounts().stream().filter(a -> a.getRole().isGreaterThan(Account.Role.USER));
        var model = new DefaultComboBoxModel<TaiKhoanComboBoxItem>();
        model.addElement(new TaiKhoanComboBoxItem(null, "-- Chọn tài khoản --"));
        accounts.forEach(a -> model.addElement(new TaiKhoanComboBoxItem(a.getId(), a.getUsername())));
        inputIdNV.setModel(model);
        inputIdNV.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                var item = (TaiKhoanComboBoxItem) e.getItem();
                var selectedRow = listEmployee.getSelectedRow();
                int employeeId;
                if (selectedRow != -1) {
                    employeeId = (int) listEmployeeModel.getValueAt(selectedRow, 0);
                } else {
                    employeeId = -1;
                }
                var exist = list.stream().anyMatch(l -> l.getAccountID() != null && l.getAccountID().equals(item.id()) && l.getId() != employeeId);
                if (exist) {
                    JOptionPane.showMessageDialog(this,
                        "⚠️ Tài khoản đã được sử dụng bởi nhân viên khác!",
                        "Cảnh báo",
                        JOptionPane.WARNING_MESSAGE);
                    inputIdNV.setSelectedIndex(0);
                } else {
                    employee.setAccountID(item.id());
                }
            }
        });
        panelBody.add(inputIdNV, gbcBody);

        // Name field
        gbcBody.gridx = 2; gbcBody.gridy = 0;
        gbcBody.fill = GridBagConstraints.NONE;
        nameNV = new JLabel("👤 Họ và tên:");
        nameNV.setFont(new Font("Segoe UI", Font.BOLD, 14));
        nameNV.setForeground(new Color(0x495057));
        panelBody.add(nameNV, gbcBody);

        gbcBody.gridx = 3; gbcBody.fill = GridBagConstraints.HORIZONTAL;
        inputNameNV = new JTextField();
        inputNameNV.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputNameNV.setPreferredSize(new Dimension(200, 35));
        inputNameNV.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        panelBody.add(inputNameNV, gbcBody);

        // Salary field
        gbcBody.gridx = 0; gbcBody.gridy = 1;
        gbcBody.fill = GridBagConstraints.NONE;
        luongNV = new JLabel("💰 Lương (VND/h):");
        luongNV.setFont(new Font("Segoe UI", Font.BOLD, 14));
        luongNV.setForeground(new Color(0x495057));
        panelBody.add(luongNV, gbcBody);

        gbcBody.gridx = 1; gbcBody.fill = GridBagConstraints.HORIZONTAL;
        inputLuongNV = new JTextField();
        inputLuongNV.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputLuongNV.setPreferredSize(new Dimension(200, 35));
        inputLuongNV.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        panelBody.add(inputLuongNV, gbcBody);

        // Phone field
        gbcBody.gridx = 2; gbcBody.gridy = 1;
        gbcBody.fill = GridBagConstraints.NONE;
        sdtNV = new JLabel("📱 Số điện thoại:");
        sdtNV.setFont(new Font("Segoe UI", Font.BOLD, 14));
        sdtNV.setForeground(new Color(0x495057));
        panelBody.add(sdtNV, gbcBody);

        gbcBody.gridx = 3; gbcBody.fill = GridBagConstraints.HORIZONTAL;
        inputSdtNV = new JTextField();
        inputSdtNV.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputSdtNV.setPreferredSize(new Dimension(200, 35));
        inputSdtNV.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        panelBody.add(inputSdtNV, gbcBody);

        // Address field (full width)
        gbcBody.gridx = 0; gbcBody.gridy = 2;
        gbcBody.fill = GridBagConstraints.NONE;
        gbcBody.gridwidth = 1;
        diachiNV = new JLabel("🏠 Địa chỉ:");
        diachiNV.setFont(new Font("Segoe UI", Font.BOLD, 14));
        diachiNV.setForeground(new Color(0x495057));
        panelBody.add(diachiNV, gbcBody);

        gbcBody.gridx = 1; gbcBody.gridwidth = 3;
        gbcBody.fill = GridBagConstraints.HORIZONTAL;
        inputDiachiNV = new JTextField();
        inputDiachiNV.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        inputDiachiNV.setPreferredSize(new Dimension(400, 35));
        inputDiachiNV.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        panelBody.add(inputDiachiNV, gbcBody);

        // Create popup menu for table
        JPopupMenu popupMenu = new JPopupMenu();
        JMenuItem menuItem = new JMenuItem("🔄 Làm mới");
        menuItem.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        menuItem.addActionListener(e -> {
            list = employeeService.findAllEmployee();
            showTable();
        });
        popupMenu.add(menuItem);

        // Modern action buttons panel
        JPanel buttonPanel = new JPanel();
        buttonPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 15, 20));
        buttonPanel.setBackground(Color.WHITE);
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));

        // Search button
        btnSearch = createModernButton("🔍 Tìm kiếm", new Color(0x17a2b8), new Color(0x138496));
        btnSearch.addActionListener(evt -> btnSearchActionPerformed(evt));

        // Add button
        btnAdd = createModernButton("➕ Thêm", new Color(0x28a745), new Color(0x218838));
        btnAdd.addActionListener(evt -> btnAddActionPerformed(evt));

        // Edit button
        btnEdit = createModernButton("✏️ Sửa", new Color(0x007bff), new Color(0x0056b3));
        btnEdit.addActionListener(evt -> btnEditActionPerformed(evt));

        // Delete button
        btnDelete = createModernButton("🗑️ Xóa", new Color(0xdc3545), new Color(0xc82333));
        btnDelete.addActionListener(evt -> btnDeleteActionPerformed(evt));

        // Clear button
        JButton btnClear = createModernButton("🧹 Xóa form", new Color(0x6c757d), new Color(0x545b62));
        btnClear.addActionListener(evt -> clearInput());

        buttonPanel.add(btnSearch);
        buttonPanel.add(btnAdd);
        buttonPanel.add(btnEdit);
        buttonPanel.add(btnDelete);
        buttonPanel.add(btnClear);

        // Create form panel with modern design
        JPanel formPanel = new JPanel(new BorderLayout());
        formPanel.setBackground(new Color(0xf8f9fa));
        formPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));
        formPanel.add(panelBody, BorderLayout.CENTER);
        formPanel.add(buttonPanel, BorderLayout.SOUTH);

        // Modern table design
        listEmployeeModel = new DefaultTableModel() {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make table read-only
            }
        };
        listEmployeeModel.addColumn("ID");
        listEmployeeModel.addColumn("👤 HỌ VÀ TÊN");
        listEmployeeModel.addColumn("🔑 TÀI KHOẢN");
        listEmployeeModel.addColumn("💰 LƯƠNG (VND/H)");
        listEmployeeModel.addColumn("📱 SỐ ĐIỆN THOẠI");
        listEmployeeModel.addColumn("🏠 ĐỊA CHỈ");
        showTable();

        listEmployee = new JTable(listEmployeeModel);

        // Modern table styling
        listEmployee.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        listEmployee.setRowHeight(35);
        listEmployee.setBackground(Color.WHITE);
        listEmployee.setForeground(new Color(0x212529));
        listEmployee.setSelectionBackground(new Color(0xe3f2fd));
        listEmployee.setSelectionForeground(new Color(0x1976d2));
        listEmployee.setGridColor(new Color(0xdee2e6));
        listEmployee.setShowGrid(true);
        listEmployee.setIntercellSpacing(new Dimension(1, 1));

        // Header styling
        listEmployee.getTableHeader().setFont(new Font("Segoe UI", Font.BOLD, 14));
        listEmployee.getTableHeader().setBackground(new Color(0x495057));
        listEmployee.getTableHeader().setForeground(Color.WHITE);
        listEmployee.getTableHeader().setPreferredSize(new Dimension(0, 45));
        listEmployee.getTableHeader().setBorder(BorderFactory.createEmptyBorder());

        // Column widths
        listEmployee.getColumnModel().getColumn(0).setPreferredWidth(50);
        listEmployee.getColumnModel().getColumn(1).setPreferredWidth(150);
        listEmployee.getColumnModel().getColumn(2).setPreferredWidth(120);
        listEmployee.getColumnModel().getColumn(3).setPreferredWidth(120);
        listEmployee.getColumnModel().getColumn(4).setPreferredWidth(130);
        listEmployee.getColumnModel().getColumn(5).setPreferredWidth(200);
        listEmployee.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                super.mouseClicked(evt);
                onSelectedRowChange();
            }

            @Override
            public void mousePressed(MouseEvent e) {
                super.mousePressed(e);
                onSelectedRowChange();
            }
        });

        // Modern scroll pane
        listEmployeeScrollPane = new JScrollPane(listEmployee);
        listEmployeeScrollPane.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        listEmployeeScrollPane.getViewport().setBackground(Color.WHITE);

        // Table title
        titleContainShowListEmployee = new JLabel("📋 Danh sách nhân viên", JLabel.CENTER);
        titleContainShowListEmployee.setFont(new Font("Segoe UI", Font.BOLD, 20));
        titleContainShowListEmployee.setForeground(new Color(0x495057));
        titleContainShowListEmployee.setBorder(BorderFactory.createEmptyBorder(15, 0, 15, 0));

        // Table container
        containShowListEmployee = new JPanel(new BorderLayout());
        containShowListEmployee.setBackground(Color.WHITE);
        containShowListEmployee.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));
        containShowListEmployee.add(titleContainShowListEmployee, BorderLayout.NORTH);
        containShowListEmployee.add(listEmployeeScrollPane, BorderLayout.CENTER);

        // Main content panel
        JPanel mainContent = new JPanel(new BorderLayout(0, 20));
        mainContent.setBackground(new Color(0xf8f9fa));
        mainContent.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        mainContent.add(formPanel, BorderLayout.NORTH);
        mainContent.add(containShowListEmployee, BorderLayout.CENTER);

        // Root panel
        managerEmployeeContentPane = new JPanel(new BorderLayout());
        managerEmployeeContentPane.setBackground(new Color(0xf8f9fa));
        managerEmployeeContentPane.add(containTitleManagerEmployee, BorderLayout.NORTH);
        managerEmployeeContentPane.add(mainContent, BorderLayout.CENTER);

        this.add(managerEmployeeContentPane, BorderLayout.CENTER);
        this.setVisible(true);
        listEmployee.setComponentPopupMenu(popupMenu);
    }

    @Override
    public void setVisible(boolean aFlag) {
        super.setVisible(aFlag);
        if (aFlag) {
            if (MainUI.getCurrentUser().getAccount().getRole().isLessThan(Account.Role.MANAGER)) {
                MainUI.getInstance().getTopNavBar().navigateTo("home");
                JOptionPane.showMessageDialog(MainUI.getInstance(), "Bạn không có quyền truy cập vào mục này");
                return;
            }
        }
        super.setVisible(aFlag);
        if (!aFlag) {
            return;
        }
        var model = new DefaultComboBoxModel<TaiKhoanComboBoxItem>();


        model.removeAllElements();
        model.addElement(new TaiKhoanComboBoxItem(null, "Không chọn"));
        List<Account> accounts = null;
        try {
            accounts = accountBUS.getAllAccounts().stream().filter(a -> a.getRole().isGreaterThan(Account.Role.USER)).toList();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        accounts.forEach(a -> model.addElement(new TaiKhoanComboBoxItem(a.getId(), a.getUsername())));
        inputIdNV.setModel(model);
    }

    public void showTable(List<Employee> list) {
        var model = (DefaultTableModel) this.listEmployeeModel;
        model.setRowCount(0);
        for (Employee e : list) {
            model.addRow(new Object[]{
                    e.getId(), e.getName(), e.getAccountID(), e.getSalaryPerHour(), e.getPhoneNumber(), e.getAddress(),
            });
        }
    }

    public void showTable() {
        showTable(this.list);

    }

    private void btnSearchActionPerformed(ActionEvent evt) {
        var searchTxt = JOptionPane.showInputDialog(null, "Nhập tên nhân viên, mã, hoặc số điện thoại", "Tìm kiếm nhân viên", JOptionPane.INFORMATION_MESSAGE);
        if (searchTxt == null) {
            return;
        }
        var fileredList = this.list.stream().filter(e -> e.getName().toLowerCase().contains(searchTxt.toLowerCase()) || String.valueOf(e.getId()).contains(searchTxt) || e.getPhoneNumber().contains(searchTxt)).toList();
        showTable(fileredList);
    }

    private void clearInput() {
        inputNameNV.setText("");
        inputLuongNV.setText("");
        inputSdtNV.setText("");
        inputIdNV.setSelectedIndex(0);
        inputDiachiNV.setText("");
    }

    private boolean validateInput() {
        try {
            Integer.parseInt(inputLuongNV.getText());
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "Lương Nhân Viên Phải Là Số", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (inputNameNV.getText().trim().equals("")) {
            JOptionPane.showMessageDialog(null, "Tên Nhân Viên Không Được Để Trống", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        if (inputLuongNV.getText().trim().equals("")) {
            JOptionPane.showMessageDialog(null, "Lương Nhân Viên Không Được Để Trống", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        if (inputSdtNV.getText().trim().equals("")) {
            JOptionPane.showMessageDialog(null, "Số Điện Thoại Không Được Để Trống", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        if (inputDiachiNV.getText().trim().equals("")) {
            JOptionPane.showMessageDialog(null, "Địa Chỉ Không Được Để Trống", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        return true;

    }

    private void btnAddActionPerformed(ActionEvent evt) {

        if (!validateInput()) {
            return;
        }
        employee.setName(inputNameNV.getText());
        employee.setSalaryPerHour(Integer.parseInt(inputLuongNV.getText()));
        employee.setPhoneNumber(inputSdtNV.getText());
        employee.setAddress(inputDiachiNV.getText());
        employee.setCreatedAt(new Date());
        try {
            employeeService.createEmployee(employee);
        } catch (SQLException ex) {
            throw new RuntimeException(ex);
        }
        JOptionPane.showMessageDialog(this, "Thêm nhân viên thành công", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        list.clear();
        list = employeeService.findAllEmployee();
        showTable();
        clearInput();
    }


    private void btnEditActionPerformed(java.awt.event.ActionEvent evt) {
        if (!validateInput()) {
            return;
        }
        var selectedRow = listEmployee.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn nhân viên để sửa", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return;
        }
        var employeeId = (int) listEmployee.getValueAt(selectedRow, 0);
        employee.setId(employeeId);
        employee.setName(inputNameNV.getText());
        employee.setSalaryPerHour(Integer.parseInt(inputLuongNV.getText()));
        employee.setPhoneNumber(inputSdtNV.getText());
        employee.setAddress(inputDiachiNV.getText());
        try {
            employeeService.updateEmployee(employee);
        } catch (SQLException ex) {
            throw new RuntimeException(ex);
        }
        JOptionPane.showMessageDialog(this, "Sửa nhân viên thành công", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        list.clear();
        list = employeeService.findAllEmployee();
        showTable();
        clearInput();
        listEmployee.clearSelection();

    }

    public void onSelectedRowChange() {
        var selectedRow = listEmployee.getSelectedRow();
        if (selectedRow == -1) {
            inputIdNV.setSelectedIndex(0);
            inputNameNV.setText("");
            inputLuongNV.setText("");
            inputSdtNV.setText("");
            inputDiachiNV.setText("");
            return;
        }
        var employeeId = (int) listEmployee.getValueAt(selectedRow, 0);
        var employee = employeeService.findEmployeeById(employeeId);
        inputNameNV.setText(employee.getName());
        inputLuongNV.setText(String.valueOf(employee.getSalaryPerHour()));
        inputSdtNV.setText(employee.getPhoneNumber());
        inputDiachiNV.setText(employee.getAddress());
        var model = (DefaultComboBoxModel<TaiKhoanComboBoxItem>) inputIdNV.getModel();
        for (int i = 1; i < model.getSize(); i++) {
            if (model.getElementAt(i).id.equals(employee.getAccountID())) {
                inputIdNV.setSelectedIndex(i);
                return;
            }
        }
    }

    private void btnDeleteActionPerformed(java.awt.event.ActionEvent evt) {
        DefaultTableModel model = (DefaultTableModel) listEmployee.getModel();
        int indexRowSelected = listEmployee.getSelectedRow();
        int idEmployeeSelected = (int) listEmployee.getValueAt(indexRowSelected, 0);
        var result = JOptionPane.showConfirmDialog(this, "Bạn có chắc chắn muốn xóa nhân viên này không?", "Xác nhận", JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.NO_OPTION || result == JOptionPane.CLOSED_OPTION) {
            return;
        }
        try {
            employeeService.delete(idEmployeeSelected);
            JOptionPane.showMessageDialog(this, "Xóa nhân viên thành công", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        model.removeRow(indexRowSelected);
    }

    public static void main(String[] args) {

    }
}