package GUI.Server.Product;

import GUI.Components.Input;
import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import DTO.Product;
import BUS.*;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProductGUI extends JPanel {
    private List<Product> list;
    private ProductBUS productBUS;
    private JPanel parentPanel, panelHeader, panelBody, panelBody1, panelBody2, buttonPanel;
    private JLabel txtListProduct, logoLabel;
    private J<PERSON>omb<PERSON>Box comboBox;
    private Input findByName;
    private JTable table;
    private JButton editButton, viewButton, deleteButton;

    private DefaultTableModel dtm;

    public ProductGUI() {
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        productBUS = ServiceProvider.getInstance().getService(ProductBUS.class);
        this.setLayout(new BorderLayout());
        this.setSize(new Dimension(screenSize.width,screenSize.height));
        initComponents();
        this.setVisible(true);
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(150, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    private void setupModernTable() {
        table.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        table.setRowHeight(35);
        table.setBackground(Color.WHITE);
        table.setForeground(new Color(0x212529));
        table.setSelectionBackground(new Color(0xe3f2fd));
        table.setSelectionForeground(new Color(0x1976d2));
        table.setGridColor(new Color(0xdee2e6));
        table.setShowGrid(true);
        table.setIntercellSpacing(new Dimension(1, 1));

        // Header styling
        table.getTableHeader().setFont(new Font("Segoe UI", Font.BOLD, 14));
        table.getTableHeader().setBackground(new Color(0x495057));
        table.getTableHeader().setForeground(Color.WHITE);
        table.getTableHeader().setPreferredSize(new Dimension(0, 45));
        table.getTableHeader().setBorder(BorderFactory.createEmptyBorder());
    }

    private void initComponents() {
        parentPanel = new JPanel();
        // Modern parent panel
        parentPanel.setBackground(new Color(0xf8f9fa));
        parentPanel.setLayout(new BorderLayout(0, 20));
        add(parentPanel, BorderLayout.CENTER);

        // Modern header design
        panelHeader = new JPanel(new BorderLayout());
        panelHeader.setPreferredSize(new Dimension(1200, 80));
        panelHeader.setBackground(new Color(0x2c3e50));
        parentPanel.add(panelHeader, BorderLayout.PAGE_START);

        // Modern title
        logoLabel = new JLabel("🛍️ Quản lý sản phẩm");
        logoLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        logoLabel.setForeground(Color.WHITE);
        logoLabel.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));
        panelHeader.add(logoLabel, BorderLayout.WEST);

        // Modern add button
        JButton addProductButton = createModernButton("➕ Tạo sản phẩm", new Color(0x28a745), new Color(0x218838));
        addProductButton.addActionListener(e -> new CreateProductGUI());
        addProductButton.setIcon(Helper.getIcon("/icons/addIcon.png", 16, 16));

        JPanel buttonContainer = new JPanel(new FlowLayout(FlowLayout.RIGHT, 30, 20));
        buttonContainer.setBackground(new Color(0x2c3e50));
        buttonContainer.add(addProductButton);
        panelHeader.add(buttonContainer, BorderLayout.EAST);

        // Modern body panel
        panelBody = new JPanel(new BorderLayout(0, 20));
        panelBody.setBackground(new Color(0xf8f9fa));
        panelBody.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        parentPanel.add(panelBody, BorderLayout.CENTER);

        // Modern filter panel
        panelBody1 = new JPanel(new BorderLayout());
        panelBody1.setBackground(Color.WHITE);
        panelBody1.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 15, 20)
        ));
        panelBody.add(panelBody1, BorderLayout.PAGE_START);

        // Modern title
        txtListProduct = new JLabel("📋 Danh sách sản phẩm");
        txtListProduct.setFont(new Font("Segoe UI", Font.BOLD, 20));
        txtListProduct.setForeground(new Color(0x495057));
        panelBody1.add(txtListProduct, BorderLayout.WEST);

        // Modern filter controls
        panelBody2 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 15, 0));
        panelBody2.setBackground(Color.WHITE);
        panelBody1.add(panelBody2, BorderLayout.EAST);


        // Modern category filter
        JLabel categoryLabel = new JLabel("🏷️ Loại:");
        categoryLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        categoryLabel.setForeground(new Color(0x495057));
        panelBody2.add(categoryLabel);

        String typeProduct[] = {"🌟 Tất cả", "🍔 Thức ăn", "🥤 Nước uống", "💳 Thẻ"};
        comboBox = new JComboBox(typeProduct);
        comboBox.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        comboBox.setPreferredSize(new Dimension(150, 35));
        comboBox.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        list = new ArrayList<>();
        var localProductService = this.productBUS;
        var dtm1 = this.dtm;
        try {
            list = localProductService.findAll();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        comboBox.addActionListener(e -> {
            String selected = (String)comboBox.getSelectedItem();
            try {
                if (selected.contains("Tất cả")) {
                    list = localProductService.findAll();
                } else if (selected.contains("Thức ăn")) {
                    list = localProductService.filterByTypeProduct(Product.ProductType.FOOD);
                } else if (selected.contains("Nước uống")){
                    list = localProductService.filterByTypeProduct(Product.ProductType.DRINK);
                } else {
                    list = localProductService.filterByTypeProduct(Product.ProductType.CARD);
                }
                showTable();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this,
                    "❌ Lỗi tải dữ liệu: " + ex.getMessage(),
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
            }
        });
        panelBody2.add(comboBox);

        // Modern search field
        JLabel searchLabel = new JLabel("🔍 Tìm kiếm:");
        searchLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        searchLabel.setForeground(new Color(0x495057));
        panelBody2.add(searchLabel);

        findByName = new Input("Nhập tên sản phẩm...");
        findByName.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        findByName.setPreferredSize(new Dimension(200, 35));
        findByName.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        findByName.addActionListener(e -> {
            try {
                String searchText = findByName.getText().trim();
                if (searchText.isEmpty() || searchText.equals("Nhập tên sản phẩm...")) {
                    list = localProductService.findAll();
                } else {
                    list = localProductService.findListByName(searchText);
                }
                showTable();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this,
                    "❌ Lỗi tìm kiếm: " + ex.getMessage(),
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
            }
        });

        // Modern popup menu
        JPopupMenu popupMenu = new JPopupMenu();
        popupMenu.setBackground(Color.WHITE);
        popupMenu.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));

        JMenuItem reset = new JMenuItem("🔄 Làm mới");
        reset.setFont(new Font("Segoe UI", Font.BOLD, 14));
        reset.setForeground(new Color(0x6c757d));
        reset.addActionListener(e -> {
            try {
                list = localProductService.findAll();
                showTable();
                comboBox.setSelectedIndex(0);
                findByName.setText("");
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this,
                    "❌ Lỗi làm mới: " + ex.getMessage(),
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
            }
        });
        popupMenu.add(reset);
        panelBody2.add(findByName);

        // Modern table design
        table = new JTable();
        setupModernTable();

        dtm = new DefaultTableModel() {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        dtm.setRowCount(0);
        table.setModel(dtm);
        dtm.addColumn("ID");
        dtm.addColumn("🛍️ Tên sản phẩm");
        dtm.addColumn("💰 Giá bán");
        dtm.addColumn("🏷️ Phân loại");
        dtm.addColumn("📝 Mô tả");
        dtm.addColumn("📦 Số lượng");
        showTable();

        // Column widths
        table.getColumnModel().getColumn(0).setPreferredWidth(60);
        table.getColumnModel().getColumn(1).setPreferredWidth(200);
        table.getColumnModel().getColumn(2).setPreferredWidth(120);
        table.getColumnModel().getColumn(3).setPreferredWidth(120);
        table.getColumnModel().getColumn(4).setPreferredWidth(250);
        table.getColumnModel().getColumn(5).setPreferredWidth(100);

        // Modern scroll pane
        var panel1 = new JScrollPane(table);
        panel1.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        panel1.getViewport().setBackground(Color.WHITE);
        panel1.setBackground(Color.WHITE);

        // Table container
        JPanel tableContainer = new JPanel(new BorderLayout());
        tableContainer.setBackground(Color.WHITE);
        tableContainer.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));
        tableContainer.add(panel1, BorderLayout.CENTER);
        panelBody.add(tableContainer, BorderLayout.CENTER);

        ListSelectionModel selectionModel = table.getSelectionModel();
        selectionModel.addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = table.getSelectedRow();
                }
            }
        });

        // Modern action buttons panel
        buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 15));
        buttonPanel.setBackground(Color.WHITE);
        buttonPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));

        editButton = createModernButton("✏️ Sửa", new Color(0x007bff), new Color(0x0056b3));
        editButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow == -1) {
                JOptionPane.showMessageDialog(this,
                    "⚠️ Vui lòng chọn sản phẩm cần sửa!",
                    "Cảnh báo",
                    JOptionPane.WARNING_MESSAGE);
                return;
            }
            new UpdateProductGUI((int)table.getValueAt(selectedRow, 0));
        });

        viewButton = createModernButton("👁️ Xem", new Color(0x17a2b8), new Color(0x138496));
        viewButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow == -1) {
                JOptionPane.showMessageDialog(this,
                    "⚠️ Vui lòng chọn sản phẩm cần xem!",
                    "Cảnh báo",
                    JOptionPane.WARNING_MESSAGE);
                return;
            }
            new ViewProductGUI((int)table.getValueAt(selectedRow, 0));
        });

        deleteButton = createModernButton("🗑️ Xóa", new Color(0xdc3545), new Color(0xc82333));
        deleteButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow == -1) {
                JOptionPane.showMessageDialog(this,
                    "⚠️ Vui lòng chọn sản phẩm cần xóa!",
                    "Cảnh báo",
                    JOptionPane.WARNING_MESSAGE);
                return;
            }

            String productName = (String) table.getValueAt(selectedRow, 1);
            int confirm = JOptionPane.showConfirmDialog(this,
                "🗑️ Bạn có chắc chắn muốn xóa sản phẩm '" + productName + "' không?\n" +
                "⚠️ Hành động này không thể hoàn tác!",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

            if (confirm == JOptionPane.YES_OPTION) {
                try {
                    int idProduct = (int)table.getValueAt(selectedRow, 0);
                    productBUS.delete(idProduct);
                    ((DefaultTableModel) table.getModel()).removeRow(selectedRow);
                    JOptionPane.showMessageDialog(this,
                        "✅ Xóa sản phẩm '" + productName + "' thành công!",
                        "Thành công",
                        JOptionPane.INFORMATION_MESSAGE);
                } catch (SQLException ex) {
                    JOptionPane.showMessageDialog(this,
                        "❌ Lỗi xóa sản phẩm: " + ex.getMessage(),
                        "Lỗi",
                        JOptionPane.ERROR_MESSAGE);
                }
            }
        });

        buttonPanel.add(editButton);
        buttonPanel.add(viewButton);
        buttonPanel.add(deleteButton);
        buttonPanel.setVisible(false);

        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = table.rowAtPoint(e.getPoint());
                if (row >= 0) {
                    buttonPanel.setVisible(true);
                    // Add visual feedback
                    table.setRowSelectionInterval(row, row);
                }
            }
        });

        // Add button panel to table container
        tableContainer.add(buttonPanel, BorderLayout.SOUTH);
        table.setComponentPopupMenu(popupMenu);

    }

    @Override
    public void setVisible(boolean aFlag) {
        super.setVisible(aFlag);
        comboBox.setSelectedIndex(0);
        if (aFlag) {
            try {
                list = this.productBUS.findAll();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            showTable();
        }
    }

    public void showTable() {
        var model = (DefaultTableModel)this.table.getModel();
        model.setRowCount(0);
        for (Product p : list) {
            model.addRow(new Object[]{
                    p.getId(), p.getName(), p.getPrice() ,p.getType(), p.getDescription(), p.getStock(),
            });
        }
    }

    private void updateTable() {
        dtm.setRowCount(0);
        String selectedItem = (String) comboBox.getSelectedItem();
        ArrayList<Product> itemList = new ArrayList<>();

        if (selectedItem.equals("Tất Cả")){
            itemList.addAll(getAllItems());
        } else if (selectedItem.equals("Thức Ăn")) {
            itemList.addAll(getFoodItems());
        } else if (selectedItem.equals("Nước Uống")) {
            itemList.addAll(getDrinkItems());
        } else {
            itemList.addAll(getCardItems());
        }

        for (Product item : itemList) {
            dtm.addRow(new Object[]{item});
        }
    }

    private ArrayList<Product> getAllItems() {
        ArrayList<Product> itemList = new ArrayList<>();
        try {
            itemList = (ArrayList<Product>) this.productBUS.findAll();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return itemList;
    }

    private ArrayList<Product> getFoodItems() {
        ArrayList<Product> itemList = new ArrayList<>();
        try {
            itemList = (ArrayList<Product>) this.productBUS.filterByTypeProduct(Product.ProductType.FOOD);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return itemList;
    }

    private ArrayList<Product> getDrinkItems() {
        ArrayList<Product> itemList = new ArrayList<>();
        try {
            itemList = (ArrayList<Product>) this.productBUS.filterByTypeProduct(Product.ProductType.DRINK);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return itemList;
    }

    private ArrayList<Product> getCardItems() {
        ArrayList<Product> itemList = new ArrayList<>();
        try {
            itemList = (ArrayList<Product>) this.productBUS.filterByTypeProduct(Product.ProductType.CARD);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return itemList;
    }

    public static void main(String[] args) {
        Helper.initUI();
        ServiceProvider.init();
        JFrame frame = new JFrame();
        frame.add(new ProductGUI());
        frame.setVisible(true);
    }
}
