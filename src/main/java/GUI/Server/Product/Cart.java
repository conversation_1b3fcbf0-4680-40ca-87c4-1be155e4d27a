package GUI.Server.Product;

import DTO.Product;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

public class Cart {
    private List<CartItem> itemList;
    private double total;

    public Cart() {
        this.itemList = new ArrayList<>();
        this.total = 0.0;
    }

    public void addItem(Product product, int quantity) {
        try {
            if (quantity <= 0) {
                JOptionPane.showMessageDialog(null,
                    "❌ Số lượng phải lớn hơn 0!",
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            if (quantity > product.getStock()) {
                JOptionPane.showMessageDialog(null,
                    "❌ Không đủ hàng trong kho! Chỉ còn " + product.getStock() + " sản phẩm.",
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            CartItem item = new CartItem(product, quantity);
            itemList.add(item);
            total += product.getPrice() * quantity;

            JOptionPane.showMessageDialog(null,
                "✅ Đã thêm " + quantity + " " + product.getName() + " vào giỏ hàng!",
                "Thành công",
                JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(null,
                "❌ Lỗi thêm sản phẩm vào giỏ hàng: " + e.getMessage(),
                "Lỗi",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    public List<CartItem> getItemList() {
        return itemList;
    }

    public double getTotal() {
        return total;
    }

    public void clearCart() {
        itemList.clear();
        total = 0.0;
    }
}
