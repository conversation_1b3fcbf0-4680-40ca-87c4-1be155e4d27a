package GUI.Server.Product;

import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import DTO.Product;
import BUS.ProductBUS;

import javax.imageio.ImageIO;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;

public class UpdateProductGUI extends JFrame {
    private JPanel parentPanel, panelHeader, panelBody, panel1, panel2, panel3, panelLeftPN, panelRightPN, imageEnd, panelPDRight, panelPDLeft, panel2d, panelRighNOP, panelRigth2, panelLeftPB, panelLeft2, panel2b, panelRigthTCB, panelRight1, panelLeftPP, panelLeft1, panel2h;
    private JButton returnButton, updateButton, chooseButton;
    private JLabel logo, productName, productPrice, productType, numberOfProduct, productDescription, productImage;
    private JTextField txtProductName, txtProductPrice, txtNumberOfProduct, txtProductDescription;
    private Product product;
    private ProductBUS productBUS;
    private JCheckBox placeBox;
    private JComboBox comboBox;

    public UpdateProductGUI(int productId) {
        productBUS = ServiceProvider.getInstance().getService(ProductBUS.class);
        try {
            product = productBUS.findById(productId);
            if (product == null) {
                JOptionPane.showMessageDialog(this,
                    "❌ Không tìm thấy sản phẩm!",
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
                this.dispose();
                return;
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this,
                "❌ Lỗi tải thông tin sản phẩm: " + e.getMessage(),
                "Lỗi",
                JOptionPane.ERROR_MESSAGE);
            this.dispose();
            return;
        }
        product.setId(productId);

        // Modern window setup
        this.setTitle("✏️ Chỉnh sửa sản phẩm - " + product.getName());
        this.setSize(900, 700);
        this.setLayout(new BorderLayout());
        this.setLocationRelativeTo(null);
        this.setResizable(false);
        this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        initComponents();
        this.setVisible(true);
    }

    private void initComponents() {
        // Modern parent panel
        parentPanel = new JPanel(new BorderLayout());
        parentPanel.setBackground(new Color(0xf8f9fa));
        add(parentPanel, BorderLayout.CENTER);

        // Modern header
        panelHeader = new JPanel(new BorderLayout());
        panelHeader.setPreferredSize(new Dimension(900, 80));
        panelHeader.setBackground(new Color(0x2c3e50));
        parentPanel.add(panelHeader, BorderLayout.NORTH);

        // Modern title
        logo = new JLabel("✏️ Chỉnh sửa thông tin sản phẩm");
        logo.setFont(new Font("Segoe UI", Font.BOLD, 24));
        logo.setForeground(Color.WHITE);
        logo.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));
        panelHeader.add(logo, BorderLayout.WEST);

        // Modern image panel
        JPanel imagePanel = createImagePanel();
        parentPanel.add(imagePanel, BorderLayout.WEST);

        // Modern form panel
        JPanel formPanel = createFormPanel();
        parentPanel.add(formPanel, BorderLayout.CENTER);
    }

    private JPanel createImagePanel() {
        JPanel imagePanel = new JPanel(new BorderLayout());
        imagePanel.setPreferredSize(new Dimension(280, 600));
        imagePanel.setBackground(Color.WHITE);
        imagePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Image title
        JLabel imageTitle = new JLabel("🖼️ Hình ảnh sản phẩm");
        imageTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        imageTitle.setForeground(new Color(0x495057));
        imageTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));
        imagePanel.add(imageTitle, BorderLayout.NORTH);

        // Image display
        JLabel imageLabel = new JLabel();
        imageLabel.setIcon(Helper.getIcon(product.getImage(), 240, 180));
        imageLabel.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        imageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        imagePanel.add(imageLabel, BorderLayout.CENTER);

        // Choose image button
        chooseButton = createModernButton("📁 Chọn ảnh", new Color(0x17a2b8), new Color(0x138496));
        chooseButton.addActionListener(e -> chooseImage(imageLabel));

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(Color.WHITE);
        buttonPanel.add(chooseButton);
        imagePanel.add(buttonPanel, BorderLayout.SOUTH);

        return imagePanel;
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    private void chooseImage(JLabel imageLabel) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
            "Image files", "jpg", "jpeg", "png", "gif"));

        int result = chooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            try {
                File selectedFile = chooser.getSelectedFile();
                String path = selectedFile.getAbsolutePath();
                var newPath = "src/main/resources/images/" + selectedFile.getName();
                BufferedImage selectedImage = ImageIO.read(new File(path));
                var newimage = new File(newPath);
                newimage.createNewFile();
                ImageIO.write(selectedImage, "png", newimage);
                product.setImage("images/" + selectedFile.getName());
                imageLabel.setIcon(new ImageIcon(selectedImage.getScaledInstance(240, 180, Image.SCALE_SMOOTH)));

                JOptionPane.showMessageDialog(this,
                    "✅ Đã chọn ảnh thành công!",
                    "Thông báo",
                    JOptionPane.INFORMATION_MESSAGE);
            } catch (IOException ex) {
                JOptionPane.showMessageDialog(this,
                    "❌ Lỗi chọn ảnh: " + ex.getMessage(),
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private JPanel createFormPanel() {
        JPanel formPanel = new JPanel(new BorderLayout());
        formPanel.setBackground(new Color(0xf8f9fa));
        formPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Form content
        JPanel formContent = new JPanel();
        formContent.setLayout(new BoxLayout(formContent, BoxLayout.Y_AXIS));
        formContent.setBackground(Color.WHITE);
        formContent.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(30, 30, 30, 30)
        ));

        // Product name field
        formContent.add(createFieldPanel("🛍️ Tên sản phẩm:",
            txtProductName = createTextField(product.getName())));
        formContent.add(Box.createVerticalStrut(20));

        // Price and type row
        JPanel priceTypePanel = new JPanel(new GridLayout(1, 2, 20, 0));
        priceTypePanel.setBackground(Color.WHITE);

        priceTypePanel.add(createFieldPanel("💰 Giá bán (VNĐ):",
            txtProductPrice = createTextField(String.valueOf(product.getPrice()))));

        priceTypePanel.add(createFieldPanel("🏷️ Loại sản phẩm:",
            comboBox = createTypeComboBox()));

        formContent.add(priceTypePanel);
        formContent.add(Box.createVerticalStrut(20));

        // Stock field
        formContent.add(createFieldPanel("📦 Số lượng trong kho:",
            txtNumberOfProduct = createTextField(String.valueOf(product.getStock()))));
        formContent.add(Box.createVerticalStrut(20));

        // Description field
        formContent.add(createFieldPanel("📝 Mô tả sản phẩm:",
            txtProductDescription = createTextField(product.getDescription())));
        formContent.add(Box.createVerticalStrut(30));

        // Action buttons
        JPanel buttonPanel = createButtonPanel();
        formContent.add(buttonPanel);

        formPanel.add(formContent, BorderLayout.CENTER);
        return formPanel;
    }

    private JPanel createFieldPanel(String labelText, JComponent field) {
        JPanel panel = new JPanel(new BorderLayout(0, 8));
        panel.setBackground(Color.WHITE);

        JLabel label = new JLabel(labelText);
        label.setFont(new Font("Segoe UI", Font.BOLD, 14));
        label.setForeground(new Color(0x495057));
        panel.add(label, BorderLayout.NORTH);
        panel.add(field, BorderLayout.CENTER);

        return panel;
    }

    private JTextField createTextField(String text) {
        JTextField textField = new JTextField(text);
        textField.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        textField.setPreferredSize(new Dimension(300, 40));
        textField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
        return textField;
    }

    private JComboBox<String> createTypeComboBox() {
        String[] types = {"🥤 Nước uống", "🍔 Thức ăn", "💳 Thẻ"};
        JComboBox<String> combo = new JComboBox<>(types);
        combo.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        combo.setPreferredSize(new Dimension(300, 40));
        combo.setBorder(BorderFactory.createLineBorder(new Color(0xced4da), 1));

        // Set current selection based on product type
        switch (product.getType()) {
            case DRINK: combo.setSelectedIndex(0); break;
            case FOOD: combo.setSelectedIndex(1); break;
            case CARD: combo.setSelectedIndex(2); break;
        }

        combo.addActionListener(e -> {
            int selectedIndex = combo.getSelectedIndex();
            switch (selectedIndex) {
                case 0: product.setType(1); break; // Drink
                case 1: product.setType(0); break; // Food
                case 2: product.setType(2); break; // Card
            }
        });

        return combo;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        buttonPanel.setBackground(Color.WHITE);

        // Save button
        updateButton = createModernButton("💾 Lưu thay đổi", new Color(0x28a745), new Color(0x218838));
        updateButton.addActionListener(this::saveProduct);

        // Cancel button
        JButton cancelButton = createModernButton("❌ Hủy", new Color(0x6c757d), new Color(0x545b62));
        cancelButton.addActionListener(e -> this.dispose());

        buttonPanel.add(updateButton);
        buttonPanel.add(cancelButton);

        return buttonPanel;
    }

    private void saveProduct(ActionEvent e) {
        // Validation
        if (txtProductName.getText().trim().isEmpty()) {
            showError("❌ Tên sản phẩm không được để trống!");
            return;
        }

        if (txtProductPrice.getText().trim().isEmpty()) {
            showError("❌ Giá bán không được để trống!");
            return;
        }

        if (!Helper.isNumber(txtProductPrice.getText().trim())) {
            showError("❌ Giá bán phải là số hợp lệ!");
            return;
        }

        if (txtNumberOfProduct.getText().trim().isEmpty()) {
            showError("❌ Số lượng không được để trống!");
            return;
        }

        if (!Helper.isNumber(txtNumberOfProduct.getText().trim())) {
            showError("❌ Số lượng phải là số nguyên hợp lệ!");
            return;
        }

        if (txtProductDescription.getText().trim().isEmpty()) {
            showError("❌ Mô tả sản phẩm không được để trống!");
            return;
        }

        try {
            // Update product data
            product.setName(txtProductName.getText().trim());
            product.setPrice(Double.parseDouble(txtProductPrice.getText().trim()));
            product.setStock(Integer.parseInt(txtNumberOfProduct.getText().trim()));
            product.setDescription(txtProductDescription.getText().trim());

            // Save to database
            productBUS.update(product);

            JOptionPane.showMessageDialog(this,
                "✅ Cập nhật sản phẩm '" + product.getName() + "' thành công!",
                "Thành công",
                JOptionPane.INFORMATION_MESSAGE);

            this.dispose();

        } catch (SQLException ex) {
            showError("❌ Lỗi cập nhật sản phẩm: " + ex.getMessage());
        } catch (NumberFormatException ex) {
            showError("❌ Dữ liệu số không hợp lệ!");
        }
    }

    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }



    public static void main(String[] args) {
    }
}
