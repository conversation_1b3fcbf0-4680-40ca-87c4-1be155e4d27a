package GUI.Server.Product;

import GUI.Components.Input;
import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import DTO.Product;
import BUS.ProductBUS;

import javax.imageio.ImageIO;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.sql.SQLException;
import java.util.Date;

public class CreateProductGUI extends JFrame {
    private JPanel parentPanel, panelHeader, panelBody, panel1, panel2, panel3, panelLeftPN, panelRightPN, imageEnd, panelPDRight, panelPDLeft, panel2d, panelRighNOP, panelRigth2, panelLeftPB, panelLeft2, panel2b, panelRigthTCB, panelRight1,panelLeftPP, panelLeft1, panel2h;
    private JButton updateButton, chooseButton;
    private JLabel logo, productName , productPrice, productType, numberOfProduct, productDescription, productImage;
    private Input txtProductName, txtProductPrice, txtNumberOfProduct, txtProductDescription;
    private Product product = Product.builder().image("/images/imageWhite.jpg").id(0).name("").price(0).createdAt(new Date()).description("").stock(0).build();
    private ProductBUS productBUS;
    private JLabel image;
    private JCheckBox placeBox;
    private String newPath;
    private JComboBox comboBox;
    public CreateProductGUI() {
        productBUS = ServiceProvider.getInstance().getService(ProductBUS.class);

        // Modern window setup
        this.setTitle("➕ Tạo sản phẩm mới");
        this.setSize(900, 700);
        this.setLayout(new BorderLayout());
        this.setLocationRelativeTo(null);
        this.setResizable(false);
        this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        initComponents();
        this.setVisible(true);
    }

    private void initComponents() {
        newPath = "";

        // Modern parent panel
        parentPanel = new JPanel(new BorderLayout());
        parentPanel.setBackground(new Color(0xf8f9fa));
        add(parentPanel, BorderLayout.CENTER);

        // Modern header
        panelHeader = new JPanel(new BorderLayout());
        panelHeader.setPreferredSize(new Dimension(900, 80));
        panelHeader.setBackground(new Color(0x2c3e50));
        parentPanel.add(panelHeader, BorderLayout.NORTH);

        // Modern title
        logo = new JLabel("➕ Tạo sản phẩm mới");
        logo.setFont(new Font("Segoe UI", Font.BOLD, 24));
        logo.setForeground(Color.WHITE);
        logo.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));
        panelHeader.add(logo, BorderLayout.WEST);

        // Modern image panel
        JPanel imagePanel = createImagePanel();
        parentPanel.add(imagePanel, BorderLayout.WEST);

        // Modern form panel
        JPanel formPanel = createFormPanel();
        parentPanel.add(formPanel, BorderLayout.CENTER);
    }


    private JPanel createImagePanel() {
        JPanel imagePanel = new JPanel(new BorderLayout());
        imagePanel.setPreferredSize(new Dimension(280, 600));
        imagePanel.setBackground(Color.WHITE);
        imagePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Image title
        JLabel imageTitle = new JLabel("🖼️ Hình ảnh sản phẩm");
        imageTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        imageTitle.setForeground(new Color(0x495057));
        imageTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));
        imagePanel.add(imageTitle, BorderLayout.NORTH);

        // Image display
        image = new JLabel();
        image.setIcon(Helper.getIcon(product.getImage(), 240, 180));
        image.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        image.setHorizontalAlignment(SwingConstants.CENTER);
        imagePanel.add(image, BorderLayout.CENTER);

        // Choose image button
        chooseButton = createModernButton("📁 Chọn ảnh", new Color(0x17a2b8), new Color(0x138496));
        chooseButton.addActionListener(e -> chooseImage());

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(Color.WHITE);
        buttonPanel.add(chooseButton);
        imagePanel.add(buttonPanel, BorderLayout.SOUTH);

        return imagePanel;
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    private void chooseImage() {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
            "Image files", "jpg", "jpeg", "png", "gif"));

        int result = chooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            try {
                File selectedFile = chooser.getSelectedFile();
                String path = selectedFile.getAbsolutePath();
                var newPatht = "src/main/resources/images/" + selectedFile.getName();
                var selectedImage = ImageIO.read(new File(path));
                var newimage = new File(newPatht);
                newimage.createNewFile();
                ImageIO.write(selectedImage, "png", newimage);
                product.setImage("images/" + selectedFile.getName());
                newPath = product.getImage();
                image.setIcon(new ImageIcon(selectedImage.getScaledInstance(240, 180, Image.SCALE_SMOOTH)));

                JOptionPane.showMessageDialog(this,
                    "✅ Đã chọn ảnh thành công!",
                    "Thông báo",
                    JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this,
                    "❌ Lỗi chọn ảnh: " + ex.getMessage(),
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private JPanel createFormPanel() {
        JPanel formPanel = new JPanel(new BorderLayout());
        formPanel.setBackground(new Color(0xf8f9fa));
        formPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Form content
        JPanel formContent = new JPanel();
        formContent.setLayout(new BoxLayout(formContent, BoxLayout.Y_AXIS));
        formContent.setBackground(Color.WHITE);
        formContent.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(30, 30, 30, 30)
        ));

        // Product name field
        formContent.add(createFieldPanel("🛍️ Tên sản phẩm:",
            txtProductName = createTextField("Nhập tên sản phẩm...")));
        formContent.add(Box.createVerticalStrut(20));

        // Price and type row
        JPanel priceTypePanel = new JPanel(new GridLayout(1, 2, 20, 0));
        priceTypePanel.setBackground(Color.WHITE);

        priceTypePanel.add(createFieldPanel("💰 Giá bán (VNĐ):",
            txtProductPrice = createTextField("Nhập giá bán...")));

        priceTypePanel.add(createFieldPanel("🏷️ Loại sản phẩm:",
            comboBox = createTypeComboBox()));

        formContent.add(priceTypePanel);
        formContent.add(Box.createVerticalStrut(20));

        // Stock field
        formContent.add(createFieldPanel("📦 Số lượng trong kho:",
            txtNumberOfProduct = createTextField("Nhập số lượng...")));
        formContent.add(Box.createVerticalStrut(20));

        // Description field
        formContent.add(createFieldPanel("📝 Mô tả sản phẩm:",
            txtProductDescription = createTextField("Nhập mô tả sản phẩm...")));
        formContent.add(Box.createVerticalStrut(30));

        // Action buttons
        JPanel buttonPanel = createButtonPanel();
        formContent.add(buttonPanel);

        formPanel.add(formContent, BorderLayout.CENTER);
        return formPanel;
    }

    private JPanel createFieldPanel(String labelText, JComponent field) {
        JPanel panel = new JPanel(new BorderLayout(0, 8));
        panel.setBackground(Color.WHITE);

        JLabel label = new JLabel(labelText);
        label.setFont(new Font("Segoe UI", Font.BOLD, 14));
        label.setForeground(new Color(0x495057));
        panel.add(label, BorderLayout.NORTH);
        panel.add(field, BorderLayout.CENTER);

        return panel;
    }

    private Input createTextField(String placeholder) {
        Input textField = new Input(placeholder);
        textField.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        textField.setPreferredSize(new Dimension(300, 40));
        textField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
        return textField;
    }

    private JComboBox<String> createTypeComboBox() {
        String[] types = {"🌟 Chọn loại sản phẩm", "🥤 Nước uống", "🍔 Thức ăn", "💳 Thẻ"};
        JComboBox<String> combo = new JComboBox<>(types);
        combo.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        combo.setPreferredSize(new Dimension(300, 40));
        combo.setBorder(BorderFactory.createLineBorder(new Color(0xced4da), 1));

        combo.addActionListener(e -> {
            int selectedIndex = combo.getSelectedIndex();
            switch (selectedIndex) {
                case 1: product.setType(1); break; // Drink
                case 2: product.setType(0); break; // Food
                case 3: product.setType(2); break; // Card
                default: product.setType(-1); break; // Not selected
            }
        });

        return combo;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        buttonPanel.setBackground(Color.WHITE);

        // Save button
        updateButton = createModernButton("💾 Tạo sản phẩm", new Color(0x28a745), new Color(0x218838));
        updateButton.addActionListener(this::saveProduct);

        // Cancel button
        JButton cancelButton = createModernButton("❌ Hủy", new Color(0x6c757d), new Color(0x545b62));
        cancelButton.addActionListener(e -> this.dispose());

        buttonPanel.add(updateButton);
        buttonPanel.add(cancelButton);

        return buttonPanel;
    }

    private void saveProduct(ActionEvent e) {
        // Comprehensive validation
        if (txtProductName.getText().trim().isEmpty()) {
            showError("❌ Tên sản phẩm không được để trống!");
            return;
        }

        if (txtProductPrice.getText().trim().isEmpty()) {
            showError("❌ Giá bán không được để trống!");
            return;
        }

        if (!Helper.isNumber(txtProductPrice.getText().trim())) {
            showError("❌ Giá bán phải là số hợp lệ!");
            return;
        }

        if (txtNumberOfProduct.getText().trim().isEmpty()) {
            showError("❌ Số lượng không được để trống!");
            return;
        }

        if (!Helper.isNumber(txtNumberOfProduct.getText().trim())) {
            showError("❌ Số lượng phải là số nguyên hợp lệ!");
            return;
        }

        if (txtProductDescription.getText().trim().isEmpty()) {
            showError("❌ Mô tả sản phẩm không được để trống!");
            return;
        }

        if (comboBox.getSelectedIndex() == 0) {
            showError("❌ Vui lòng chọn loại sản phẩm!");
            return;
        }

        if (newPath.isEmpty()) {
            showError("❌ Vui lòng chọn hình ảnh sản phẩm!");
            return;
        }

        try {
            // Validate numbers
            double price = Double.parseDouble(txtProductPrice.getText().trim());
            int stock = Integer.parseInt(txtNumberOfProduct.getText().trim());

            if (price <= 0) {
                showError("❌ Giá bán phải lớn hơn 0!");
                return;
            }

            if (stock < 0) {
                showError("❌ Số lượng không được âm!");
                return;
            }

            // Update product data
            product.setName(txtProductName.getText().trim());
            product.setPrice(price);
            product.setStock(stock);
            product.setDescription(txtProductDescription.getText().trim());
            product.setCreatedAt(new Date());

            // Save to database
            productBUS.create(product);

            JOptionPane.showMessageDialog(this,
                "✅ Tạo sản phẩm '" + product.getName() + "' thành công!",
                "Thành công",
                JOptionPane.INFORMATION_MESSAGE);

            this.dispose();

        } catch (SQLException ex) {
            showError("❌ Lỗi tạo sản phẩm: " + ex.getMessage());
        } catch (NumberFormatException ex) {
            showError("❌ Dữ liệu số không hợp lệ!");
        }
    }

    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    public static void main(String[] args) {
        Helper.initUI();
        ServiceProvider.init();
        new CreateProductGUI();
    }
}
