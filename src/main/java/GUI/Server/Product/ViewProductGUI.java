package GUI.Server.Product;

import Utils.Fonts;
import Utils.Helper;
import Utils.ServiceProvider;
import DTO.Product;
import BUS.ProductBUS;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.sql.SQLException;
import java.util.Date;

public class ViewProductGUI extends JFrame {
    private JPanel parentPanel, panelHeader, panelBody, panel1, panel2, panel3, panelLeftPN, panelRightPN, imageEnd, panelPDRight, panelPDLeft, panel2d, panelRighNOP, panelRigth2, panelLeftPB, panelLeft2, panel2b, panelRigthTCB, panelRight1,panelLeftPP, panelLeft1, panel2h;
    private JButton returnButton, updateButton, chooseButton;
    private JLabel logo, productName , productPrice, productType, numberOfProduct, productDescription, productImage;
    private JTextField txtProductName, txtProductPrice, txtNumberOfProduct, txtProductDescription;
    private Product product = Product.builder().image("/images/gtaV.jpg").id(0).name("").price(0).createdAt(new Date()).description("").stock(0).build();
    private ProductBUS productBUS;
    private JCheckBox placeBox;
    private JComboBox comboBox;
    public ViewProductGUI(int productId) {
        productBUS = ServiceProvider.getInstance().getService(ProductBUS.class);
        try {
            product = productBUS.findById(productId);
            if (product == null) {
                JOptionPane.showMessageDialog(this,
                    "❌ Không tìm thấy sản phẩm!",
                    "Lỗi",
                    JOptionPane.ERROR_MESSAGE);
                this.dispose();
                return;
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this,
                "❌ Lỗi tải thông tin sản phẩm: " + e.getMessage(),
                "Lỗi",
                JOptionPane.ERROR_MESSAGE);
            this.dispose();
            return;
        }

        // Modern window setup
        this.setTitle("👁️ Thông tin sản phẩm - " + product.getName());
        this.setSize(900, 700);
        this.setLayout(new BorderLayout());
        this.setLocationRelativeTo(null);
        this.setResizable(false);
        this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        initComponents();
        this.setVisible(true);
    }

    private void initComponents() {
        // Modern parent panel
        parentPanel = new JPanel(new BorderLayout());
        parentPanel.setBackground(new Color(0xf8f9fa));
        add(parentPanel, BorderLayout.CENTER);

        // Modern header
        panelHeader = new JPanel(new BorderLayout());
        panelHeader.setPreferredSize(new Dimension(900, 80));
        panelHeader.setBackground(new Color(0x2c3e50));
        parentPanel.add(panelHeader, BorderLayout.NORTH);

        // Modern title
        logo = new JLabel("👁️ Thông tin sản phẩm");
        logo.setFont(new Font("Segoe UI", Font.BOLD, 24));
        logo.setForeground(Color.WHITE);
        logo.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 0));
        panelHeader.add(logo, BorderLayout.WEST);

        // Close button
        JButton closeButton = createModernButton("❌ Đóng", new Color(0x6c757d), new Color(0x545b62));
        closeButton.addActionListener(e -> this.dispose());

        JPanel buttonContainer = new JPanel(new FlowLayout(FlowLayout.RIGHT, 30, 20));
        buttonContainer.setBackground(new Color(0x2c3e50));
        buttonContainer.add(closeButton);
        panelHeader.add(buttonContainer, BorderLayout.EAST);

        // Modern image panel
        JPanel imagePanel = createImagePanel();
        parentPanel.add(imagePanel, BorderLayout.WEST);

        // Modern info panel
        JPanel infoPanel = createInfoPanel();
        parentPanel.add(infoPanel, BorderLayout.CENTER);
    }

    private JButton createModernButton(String text, Color bgColor, Color hoverColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 13));
        button.setForeground(Color.WHITE);
        button.setBackground(bgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));

        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    private JPanel createImagePanel() {
        JPanel imagePanel = new JPanel(new BorderLayout());
        imagePanel.setPreferredSize(new Dimension(280, 600));
        imagePanel.setBackground(Color.WHITE);
        imagePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Image title
        JLabel imageTitle = new JLabel("🖼️ Hình ảnh sản phẩm");
        imageTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        imageTitle.setForeground(new Color(0x495057));
        imageTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));
        imagePanel.add(imageTitle, BorderLayout.NORTH);

        // Image display
        JLabel imageLabel = new JLabel();
        imageLabel.setIcon(Helper.getIcon(product.getImage(), 240, 180));
        imageLabel.setBorder(BorderFactory.createLineBorder(new Color(0xdee2e6), 1));
        imageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        imagePanel.add(imageLabel, BorderLayout.CENTER);

        return imagePanel;
    }

    private JPanel createInfoPanel() {
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBackground(new Color(0xf8f9fa));
        infoPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Info content
        JPanel infoContent = new JPanel();
        infoContent.setLayout(new BoxLayout(infoContent, BoxLayout.Y_AXIS));
        infoContent.setBackground(Color.WHITE);
        infoContent.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xdee2e6), 1),
            BorderFactory.createEmptyBorder(30, 30, 30, 30)
        ));

        // Product name field
        infoContent.add(createReadOnlyFieldPanel("🛍️ Tên sản phẩm:", product.getName()));
        infoContent.add(Box.createVerticalStrut(20));

        // Price and type row
        JPanel priceTypePanel = new JPanel(new GridLayout(1, 2, 20, 0));
        priceTypePanel.setBackground(Color.WHITE);

        priceTypePanel.add(createReadOnlyFieldPanel("💰 Giá bán:",
            Helper.formatMoney(product.getPrice()) + " VNĐ"));

        String typeText = "";
        switch (product.getType()) {
            case FOOD: typeText = "🍔 Thức ăn"; break;
            case DRINK: typeText = "🥤 Nước uống"; break;
            case CARD: typeText = "💳 Thẻ"; break;
            default: typeText = "❓ Không xác định"; break;
        }
        priceTypePanel.add(createReadOnlyFieldPanel("🏷️ Loại sản phẩm:", typeText));

        infoContent.add(priceTypePanel);
        infoContent.add(Box.createVerticalStrut(20));

        // Stock field
        infoContent.add(createReadOnlyFieldPanel("📦 Số lượng trong kho:",
            String.valueOf(product.getStock()) + " sản phẩm"));
        infoContent.add(Box.createVerticalStrut(20));

        // Description field
        infoContent.add(createReadOnlyFieldPanel("📝 Mô tả sản phẩm:", product.getDescription()));
        infoContent.add(Box.createVerticalStrut(20));

        // Created date field
        infoContent.add(createReadOnlyFieldPanel("📅 Ngày tạo:",
            Helper.getDateString(product.getCreatedAt())));

        infoPanel.add(infoContent, BorderLayout.CENTER);
        return infoPanel;
    }

    private JPanel createReadOnlyFieldPanel(String labelText, String value) {
        JPanel panel = new JPanel(new BorderLayout(0, 8));
        panel.setBackground(Color.WHITE);

        JLabel label = new JLabel(labelText);
        label.setFont(new Font("Segoe UI", Font.BOLD, 14));
        label.setForeground(new Color(0x495057));
        panel.add(label, BorderLayout.NORTH);

        JTextField textField = new JTextField(value);
        textField.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        textField.setPreferredSize(new Dimension(300, 40));
        textField.setEditable(false);
        textField.setBackground(new Color(0xf8f9fa));
        textField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0xced4da), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
        panel.add(textField, BorderLayout.CENTER);

        return panel;
    }



    public static void main(String[] args) {

    }
}
