package GUI.Client;

import DTO.Product;
import GUI.Server.Order.FoodOrder;
import Io.Socket;
import Utils.Constants;
import Utils.Helper;
import DTO.Session;


import javax.swing.*;
import java.awt.*;
import java.io.IOException;
import java.util.List;

public class Main {
    private static final int COMPUTER_ID = 1;
    public static final Socket socket;

    static {
        try {
            // địa chỉ ip
            socket = new Socket("************", Constants.SOCKET_PORT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static Session session;

    public static void main(String[] args) {
        System.out.println("DEBUG: Starting client application");
        System.out.println("DEBUG: Computer ID: " + COMPUTER_ID);
        socket.emit("identify", COMPUTER_ID);
        System.out.println("DEBUG: Sent identify request");
        socket.on("errorMessage", (c,data) -> {
            System.out.println("DEBUG: Received errorMessage: " + data);
            JOptionPane.showMessageDialog(null, data, "Lỗi", JOptionPane.ERROR_MESSAGE);
        });
        socket.on("infoMessage", (c,data) -> {
            System.out.println("DEBUG: Received infoMessage: " + data);
            JOptionPane.showMessageDialog(null, data, "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        });
        socket.on("notification", (c,data) -> {
            Helper.showSystemNoitification("Thông báo", (String) data, TrayIcon.MessageType.INFO);
        });
        socket.on("listProduct",(c,data)->{
            List<Product> products = (List<Product>) data;
            FoodOrder.products = products;
        });
        Helper.initUI();
        //shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                socket.emit("shutdown",null);
                socket.disconnect();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }));

        new LoginGUI();
    }
}
