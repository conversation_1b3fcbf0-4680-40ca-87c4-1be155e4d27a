package Repository;

import DTO.Account;
import Utils.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Repository đơn giản cho Account entity
 */
public class AccountRepository extends BaseRepository<Account, Integer> {
    private static final Logger logger = Logger.getLogger(AccountRepository.class.getName());

    public AccountRepository() {
        super(Account.class);
    }

    /**
     * Tìm account theo username
     */
    public Optional<Account> findByUsername(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.username = :username AND a.deletedAt IS NULL", Account.class);
            query.setParameter("username", username);
            return query.uniqueResultOptional();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding account by username", e);
            return Optional.empty();
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * Tìm account theo username và password (cho login)
     */
    public Optional<Account> findByUsernameAndPassword(String username, String password) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.username = :username AND a.password = :password AND a.deletedAt IS NULL",
                Account.class);
            query.setParameter("username", username);
            query.setParameter("password", password);
            return query.uniqueResultOptional();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding account by username and password", e);
            return Optional.empty();
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }
}
