package Repository;

import Utils.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Base Repository đơn giản với CRUD operations
 * @param <T> Entity type
 * @param <ID> Primary key type
 */
public abstract class BaseRepository<T, ID extends Serializable> {
    private static final Logger logger = Logger.getLogger(BaseRepository.class.getName());

    protected final Class<T> entityClass;

    protected BaseRepository(Class<T> entityClass) {
        this.entityClass = entityClass;
    }
    
    /**
     * Tìm entity theo ID
     */
    public Optional<T> findById(ID id) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            T entity = session.get(entityClass, id);
            return Optional.ofNullable(entity);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding entity by ID", e);
            return Optional.empty();
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * Lưu entity mới
     */
    public T save(T entity) {
        Session session = null;
        Transaction transaction = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            transaction = session.beginTransaction();
            session.persist(entity);
            transaction.commit();
            return entity;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.log(Level.SEVERE, "Error saving entity", e);
            throw new RuntimeException("Save failed", e);
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * Cập nhật entity
     */
    public T update(T entity) {
        Session session = null;
        Transaction transaction = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            transaction = session.beginTransaction();
            T updated = session.merge(entity);
            transaction.commit();
            return updated;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.log(Level.SEVERE, "Error updating entity", e);
            throw new RuntimeException("Update failed", e);
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * Tìm tất cả entities
     */
    public List<T> findAll() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<T> query = session.createQuery("FROM " + entityClass.getSimpleName(), entityClass);
            return query.getResultList();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding all entities", e);
            throw new RuntimeException("Find all failed", e);
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * Đếm tổng số entities
     */
    public long count() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<Long> query = session.createQuery("SELECT COUNT(e) FROM " + entityClass.getSimpleName() + " e", Long.class);
            return query.getSingleResult();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error counting entities", e);
            return 0;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }
    

}
