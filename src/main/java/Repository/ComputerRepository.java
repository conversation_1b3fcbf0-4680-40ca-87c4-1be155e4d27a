package Repository;

import DTO.Computer;
import Utils.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Repository đơn g<PERSON>ản cho Computer entity
 */
public class ComputerRepository extends BaseRepository<Computer, Integer> {
    private static final Logger logger = Logger.getLogger(ComputerRepository.class.getName());

    public ComputerRepository() {
        super(Computer.class);
    }

    /**
     * Tìm computer theo tên
     */
    public Optional<Computer> findByName(String name) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.name = :name AND c.deletedAt IS NULL", Computer.class);
            query.setParameter("name", name);
            return query.uniqueResultOptional();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding computer by name", e);
            return Optional.empty();
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }
    
    /**
     * Tìm computers theo status
     */
    public List<Computer> findByStatus(Computer.ComputerStatus status) {
        return executeWithSession(session -> {
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.status = :status AND c.deletedAt IS NULL ORDER BY c.name", 
                Computer.class);
            query.setParameter("status", status);
            return query.getResultList();
        });
    }
    
    /**
     * Tìm computers theo type
     */
    public List<Computer> findByType(Computer.ComputerType type) {
        return executeWithSession(session -> {
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.type = :type AND c.deletedAt IS NULL ORDER BY c.name", 
                Computer.class);
            query.setParameter("type", type);
            return query.getResultList();
        });
    }
    
    /**
     * Tìm computers available (OFF status)
     */
    public List<Computer> findAvailableComputers() {
        return findByStatus(Computer.ComputerStatus.OFF);
    }
    
    /**
     * Tìm computers đang sử dụng (USING status)
     */
    public List<Computer> findComputersInUse() {
        return findByStatus(Computer.ComputerStatus.USING);
    }
    
    /**
     * Cập nhật status của computer
     */
    public void updateStatus(Integer computerId, Computer.ComputerStatus newStatus) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Computer c SET c.status = :status WHERE c.id = :id");
            query.setParameter("status", newStatus);
            query.setParameter("id", computerId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Cập nhật giá của computer
     */
    public void updatePrice(Integer computerId, double newPrice) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Computer c SET c.price = :price WHERE c.id = :id");
            query.setParameter("price", newPrice);
            query.setParameter("id", computerId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Tìm computers theo khoảng giá
     */
    public List<Computer> findByPriceRange(double minPrice, double maxPrice) {
        return executeWithSession(session -> {
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.price BETWEEN :minPrice AND :maxPrice AND c.deletedAt IS NULL ORDER BY c.price", 
                Computer.class);
            query.setParameter("minPrice", minPrice);
            query.setParameter("maxPrice", maxPrice);
            return query.getResultList();
        });
    }
    
    /**
     * Soft delete computer
     */
    public void softDelete(Integer computerId) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Computer c SET c.deletedAt = CURRENT_TIMESTAMP WHERE c.id = :id");
            query.setParameter("id", computerId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Tìm tất cả computers chưa bị xóa
     */
    public List<Computer> findAllActive() {
        return executeWithSession(session -> {
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.deletedAt IS NULL ORDER BY c.name", Computer.class);
            return query.getResultList();
        });
    }
    
    /**
     * Tìm computers theo tên (like search)
     */
    public List<Computer> findByNameContaining(String searchTerm) {
        return executeWithSession(session -> {
            Query<Computer> query = session.createQuery(
                "FROM Computer c WHERE c.name LIKE :searchTerm AND c.deletedAt IS NULL ORDER BY c.name", 
                Computer.class);
            query.setParameter("searchTerm", "%" + searchTerm + "%");
            return query.getResultList();
        });
    }
    
    /**
     * Kiểm tra tên computer đã tồn tại
     */
    public boolean existsByName(String name) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(c) FROM Computer c WHERE c.name = :name AND c.deletedAt IS NULL", Long.class);
            query.setParameter("name", name);
            return query.getSingleResult() > 0;
        });
    }
    
    /**
     * Đếm computers theo status
     */
    public long countByStatus(Computer.ComputerStatus status) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(c) FROM Computer c WHERE c.status = :status AND c.deletedAt IS NULL", Long.class);
            query.setParameter("status", status);
            return query.getSingleResult();
        });
    }
    
    /**
     * Đếm computers theo type
     */
    public long countByType(Computer.ComputerType type) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(c) FROM Computer c WHERE c.type = :type AND c.deletedAt IS NULL", Long.class);
            query.setParameter("type", type);
            return query.getSingleResult();
        });
    }
    
    /**
     * Lấy thống kê computers theo status
     */
    public List<Object[]> getStatusStatistics() {
        return executeWithSession(session -> {
            Query<Object[]> query = session.createQuery(
                "SELECT c.status, COUNT(c) FROM Computer c WHERE c.deletedAt IS NULL GROUP BY c.status", 
                Object[].class);
            return query.getResultList();
        });
    }
    
    /**
     * Lấy thống kê computers theo type
     */
    public List<Object[]> getTypeStatistics() {
        return executeWithSession(session -> {
            Query<Object[]> query = session.createQuery(
                "SELECT c.type, COUNT(c), AVG(c.price) FROM Computer c WHERE c.deletedAt IS NULL GROUP BY c.type", 
                Object[].class);
            return query.getResultList();
        });
    }
}
