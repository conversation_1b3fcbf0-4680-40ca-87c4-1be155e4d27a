package DTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


@Data // tự động sinh ra getter setter toString equals hashCode tự động
@NoArgsConstructor // tự động tạo ra các constructor khoonong có tham số
@AllArgsConstructor // tự động sinh ra các constructor với đầy đủ các tham số
@Builder // hỗ trợ tạo ra các pattern Builder tạo các object linh hoạt
public class Employee {
    // các dối tượng của nhân viên
    private int id; //id

    private String name; //tên

    // khi chưa liên kết id gắn với nhân viên (null nếu chưa liên kết)
    private Integer accountID = null;


    private Account account;

    // các thông tin khác
    private String otherInformation;
    // lương theo giờ mặc định là 0
    private int salaryPerHour =0;

    private String phoneNumber;
    private String address;


    private Date createdAt = new Date(); // ngày tài khoản được tạo
    private Date deletedAt; // ngày tài khoản bị xóa

    // danh sách hóa đơn
    private List<Invoice> createdInvoices;

    // constructor sao chép (coppy constructor ): tạo một bản sao tuwf 1 nhân viên
    public Employee(Employee other){
        this.id = other.id;
        this.name = other.name;
        this.accountID = other.accountID;
        this.account = other.account;
        this.otherInformation = other.otherInformation;
        this.salaryPerHour = other.salaryPerHour;
        this.phoneNumber = other.phoneNumber;
        this.address = other.address;
        this.createdAt = other.createdAt;
        this.deletedAt = other.deletedAt;
        this.createdInvoices = other.createdInvoices;

    }
}
