package DTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "computer")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Computer implements Serializable {
    @Serial
    private static final long serialVersionUID = 746559035L;
    public enum ComputerType {
        Vip,
        Normal,
        ;
        @Override
        public String toString() {
            return switch (this){
                case Vip -> "Máy Vip";
                case Normal -> "<PERSON><PERSON><PERSON> thường";
            };
        }
    }
    public  enum ComputerStatus {
        MAINTAINING,
        LOCKED,
        OFF,
        USING,
        ;

        @Override
        public String toString() {
            return switch (this) {
                case MAINTAINING -> "đang bảo trì";
                case LOCKED -> "đang khóa";
                case OFF -> "đang tắt";
                case USING -> "đang dùng";
            };
        }
    }
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private int id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "price", nullable = false)
    private double price;// giá tiền trên 1 giờ

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "type", nullable = false)
    private ComputerType type;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "status", nullable = false)
    private ComputerStatus status = ComputerStatus.OFF;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "createdAt")
    private Date createdAt = new Date();

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "deletedAt")
    private Date deletedAt = null;

    // TODO: Uncomment when these classes are ready
    // private List<ComputerUsage> computerUsages;
    // private List<Invoice> invoices;
    // private Session currentSession;
    public void setStatus(Integer status) {
        this.status = ComputerStatus.values()[status];
    }
    public void setType(Integer type) {
        this.type = ComputerType.values()[type];
    }
    public void setType(String type) {
        if (type == null || type.trim().isEmpty()) {
            this.type = ComputerType.Normal; // Default to Normal
            return;
        }

        switch (type.trim()) {
            case "Máy VIP", "Máy Vip", "VIP", "Vip" -> this.type = ComputerType.Vip;
            case "Máy thường", "Máy thuong", "Normal", "normal", "Thường", "thuong" -> this.type = ComputerType.Normal;
            default -> {
                System.err.println("WARNING: Unknown computer type: '" + type + "'. Using Normal as default.");
                this.type = ComputerType.Normal;
            }
        }
    }

}
