package DTO;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder

public class Product implements Serializable {
    @Serial
    private static final long serialVersionUID = 214560262412L;

    public enum ProductType {
        FOOD,
        DRINK,
        CARD,
        ;

        @Override
        public String toString() {
            return switch (this) {
                case FOOD -> "đồ ăn";
                case DRINK -> "nước uống";
                case CARD -> "thẻ";
            };
        }
    }
    private Integer id;

    private String name;

    private double price;

    private ProductType type;

    private int stock;

    private String description;

    private String image;

    private Date createdAt;

    private Date deletedAt;

    private List<InvoiceDetail> invoiceDetails;


    public void setType(Integer productType) {
        this.type = ProductType.values()[productType];
    }
}
