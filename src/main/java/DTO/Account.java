package DTO;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serial;
import java.util.List;


@Entity
@Table(name = "account")
@Data // tự ộng tạo getter, setter , equals, hasshCode, toString
@NoArgsConstructor // tạo constructor không tham số
@AllArgsConstructor // tạo constructor có tham số đầy đủ
@Builder // hỗ trợ khởi tạo bằng Builder pattern
public class Account implements java.io.Serializable { // cho phép đối tượng được tuần tự hoóa để lưu trữ / ghi file
    public static Account getInstance() {
        return new Account(); // phương thức tiện lợi để tạo project mới đây không phải singleton
    }
    @Serial
    private static final long serialVersionUID = 67566435324L; // dùng để phiên bản hóa object khi Serialize
    public enum Role { // enum role định nghĩa vai trò của người dùng trong hệ thống
        ADMIN,     // quản trị hệ thống
        MANAGER,   // quản lí quán
        EMPLOYEE,  // nhân viên phục vụ
        USER;      // người dùng

        @Override
        public String toString() {
            return switch (this) {
                case ADMIN -> "Admin";
                case MANAGER -> "Quản lý";
                case EMPLOYEE -> "Nhân viên";
                case USER -> "Khách hàng";
            }; // ghi đè lại tiếng Việt
        }
        public boolean isGreaterThan(Role role){
            return role.ordinal() > this.ordinal(); // so sánh quyên hạn ccao hơn
        }
        public boolean isLessThan(Role role){
            return role.ordinal()< this.ordinal(); // hay thấp hơn
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private int id; // ID tài khoản (khóa chính )

    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username; // tên đăng nhập

    @Column(name = "password", nullable = false, length = 255)
    private String password; // mật khẩu

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "role", nullable = false)
    private Role role = Role.USER; // luôn mặc định là người dùng bình thường

    @Column(name = "balance", nullable = false)
    private double balance = 0; // số tiền trong tài khoản

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "createdAt")
    private java.util.Date createdAt; // ngày tạo tài khoản

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "deletedAt")
    private java.util.Date deletedAt; // ngày tài khoản bị xóa (sẽ là giá trị null nếu như chưa xóa)

    // TODO: Uncomment when these classes are ready
    // private List<ComputerUsage> usingHistory; // danh sách lịch sử sử dụng máy tính
    // private List<Invoice> invoices; // danh sách hóa đơn người dùng
    // private Session currentSession = null; // phiên bản sử dụng máy tính hiện tại(nếu có )
    // private Employee employee; // nếu tài khoản là nhaan viên thì gán thông tin tương ứng

    public void setRole(Integer role) {
        // gán giá trị enum role bắt đầu từ số 0
        this.role = switch (role) {
            case 0 -> Role.ADMIN;
            case 1 -> Role.MANAGER;
            case 2 -> Role.EMPLOYEE;
            default -> Role.USER;
        };
    }

}
