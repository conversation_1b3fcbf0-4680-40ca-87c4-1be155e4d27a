package Utils;

import Repository.*;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Service Provider đ<PERSON>n gi<PERSON>n cho Hibernate repositories
 */
public class HibernateServiceProvider {
    private static final Logger logger = Logger.getLogger(HibernateServiceProvider.class.getName());
    private static HibernateServiceProvider instance;
    private final Map<Class<?>, Object> services = new HashMap<>();

    private HibernateServiceProvider() {
        initializeRepositories();
    }

    /**
     * Lấy singleton instance
     */
    public static HibernateServiceProvider getInstance() {
        if (instance == null) {
            instance = new HibernateServiceProvider();
        }
        return instance;
    }

    /**
     * Khởi tạo repositories
     */
    private void initializeRepositories() {
        try {
            logger.info("Initializing Hibernate repositories...");

            // Khởi tạo repositories
            services.put(AccountRepository.class, new AccountRepository());
            services.put(ComputerRepository.class, new ComputerRepository());

            logger.info("Hibernate repositories initialized successfully");

        } catch (Exception e) {
            logger.severe("Failed to initialize Hibernate repositories: " + e.getMessage());
            throw new RuntimeException("Repository initialization failed", e);
        }
    }
    
    /**
     * Lấy repository theo class type
     */
    @SuppressWarnings("unchecked")
    public <T> T getRepository(Class<T> repositoryClass) {
        Object repository = services.get(repositoryClass);
        if (repository == null) {
            throw new IllegalArgumentException("Repository not found: " + repositoryClass.getName());
        }
        return (T) repository;
    }

    /**
     * Shutdown repositories và Hibernate
     */
    public void shutdown() {
        try {
            logger.info("Shutting down Hibernate Service Provider...");
            services.clear();
            HibernateUtil.shutdown();
            logger.info("Hibernate Service Provider shutdown completed");
        } catch (Exception e) {
            logger.severe("Error during shutdown: " + e.getMessage());
        }
    }

    /**
     * Khởi tạo static method
     */
    public static void init() {
        getInstance();
        logger.info("Hibernate Service Provider initialized");
    }
}
