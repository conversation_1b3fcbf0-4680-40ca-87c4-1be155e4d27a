package Utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Utility class for password hashing and verification using SHA-256 with salt
 * Provides secure password storage and verification methods
 */
public class PasswordUtils {
    
    private static final String HASH_ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 8; // 8 bytes = 64 bits (shorter for database compatibility)
    private static final String SEPARATOR = ":"; // Separator between salt and hash
    
    /**
     * Generates a random salt for password hashing
     * @return Base64 encoded salt string
     */
    private static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * Hashes a password with salt using SHA-256
     * @param password The plain text password to hash
     * @param salt The salt to use for hashing
     * @return SHA-256 hash as hex string
     * @throws RuntimeException if hashing algorithm is not available
     */
    private static String hashPasswordWithSalt(String password, String salt) {
        try {
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            
            // Combine password and salt
            String saltedPassword = password + salt;
            byte[] hashBytes = digest.digest(saltedPassword.getBytes(StandardCharsets.UTF_8));
            
            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
            
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
    
    /**
     * Hashes a password with a new random salt
     * @param password The plain text password to hash
     * @return String in format "salt:hash" where both are encoded (max 64 chars for DB compatibility)
     */
    public static String hashPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("Password cannot be null or empty");
        }

        String salt = generateSalt();
        String hash = hashPasswordWithSalt(password, salt);

        // Truncate hash to first 32 characters for database compatibility
        // Still secure as 32 hex chars = 128 bits of entropy
        String truncatedHash = hash.length() > 32 ? hash.substring(0, 32) : hash;

        // Return salt:hash format for storage (total ~45 chars)
        return salt + SEPARATOR + truncatedHash;
    }
    
    /**
     * Verifies a password against a stored hash
     * @param password The plain text password to verify
     * @param storedHash The stored hash in format "salt:hash"
     * @return true if password matches, false otherwise
     */
    public static boolean verifyPassword(String password, String storedHash) {
        if (password == null || storedHash == null) {
            return false;
        }
        
        try {
            // Split stored hash into salt and hash parts
            String[] parts = storedHash.split(SEPARATOR, 2);
            if (parts.length != 2) {
                // Handle legacy plain text passwords
                return password.equals(storedHash);
            }
            
            String salt = parts[0];
            String expectedHash = parts[1];
            
            // Hash the provided password with the stored salt
            String actualHash = hashPasswordWithSalt(password, salt);

            // Truncate actual hash to match stored hash length for comparison
            String truncatedActualHash = actualHash.length() > expectedHash.length() ?
                actualHash.substring(0, expectedHash.length()) : actualHash;

            // Compare hashes using constant-time comparison to prevent timing attacks
            return constantTimeEquals(expectedHash, truncatedActualHash);
            
        } catch (Exception e) {
            // If any error occurs, return false for security
            return false;
        }
    }
    
    /**
     * Constant-time string comparison to prevent timing attacks
     * @param a First string
     * @param b Second string
     * @return true if strings are equal, false otherwise
     */
    private static boolean constantTimeEquals(String a, String b) {
        if (a.length() != b.length()) {
            return false;
        }
        
        int result = 0;
        for (int i = 0; i < a.length(); i++) {
            result |= a.charAt(i) ^ b.charAt(i);
        }
        return result == 0;
    }
    
    /**
     * Checks if a stored password is in plain text format
     * @param storedPassword The stored password to check
     * @return true if it's plain text (no salt separator), false if it's hashed
     */
    public static boolean isPlainTextPassword(String storedPassword) {
        return storedPassword != null && !storedPassword.contains(SEPARATOR);
    }
    
    /**
     * Migrates a plain text password to hashed format
     * @param plainTextPassword The plain text password
     * @return Hashed password in salt:hash format
     */
    public static String migratePlainTextPassword(String plainTextPassword) {
        return hashPassword(plainTextPassword);
    }
    
    /**
     * Validates password strength (basic validation)
     * @param password The password to validate
     * @return true if password meets basic requirements
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 6) {
            return false;
        }
        
        // Add more validation rules as needed
        // - At least one uppercase letter
        // - At least one lowercase letter  
        // - At least one digit
        // - At least one special character
        
        return true; // Basic validation for now
    }
    
    /**
     * Generates a secure random password
     * @param length The desired password length
     * @return A randomly generated password
     */
    public static String generateSecurePassword(int length) {
        if (length < 8) {
            throw new IllegalArgumentException("Password length should be at least 8 characters");
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }
}
