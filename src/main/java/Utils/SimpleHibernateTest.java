package Utils;

import Repository.AccountRepository;
import DTO.Account;

import java.util.List;
import java.util.logging.Logger;

/**
 * Test đơn giản cho Hibernate
 */
public class SimpleHibernateTest {
    private static final Logger logger = Logger.getLogger(SimpleHibernateTest.class.getName());
    
    public static void main(String[] args) {
        try {
            logger.info("Starting Simple Hibernate Test...");
            
            // Initialize Hibernate
            HibernateServiceProvider.init();
            
            // Test basic operations
            testBasicOperations();
            
            logger.info("Simple Hibernate Test completed successfully");
            
        } catch (Exception e) {
            logger.severe("Simple Hibernate Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Cleanup
            HibernateServiceProvider.getInstance().shutdown();
        }
    }
    
    private static void testBasicOperations() {
        logger.info("=== Testing Basic Operations ===");
        
        AccountRepository accountRepo = HibernateServiceProvider.getInstance()
                .getRepository(AccountRepository.class);
        
        try {
            // Test count
            long count = accountRepo.count();
            logger.info("Total accounts: " + count);
            
            // Test find all
            List<Account> accounts = accountRepo.findAll();
            logger.info("Found accounts: " + accounts.size());
            
            // Test create if no accounts exist
            if (accounts.isEmpty()) {
                logger.info("No accounts found, creating test account...");
                
                Account testAccount = Account.builder()
                        .username("admin")
                        .password("admin123")
                        .role(Account.Role.ADMIN)
                        .balance(0.0)
                        .build();
                
                Account saved = accountRepo.save(testAccount);
                logger.info("Created account with ID: " + saved.getId());
            }
            
            // Test find by username
            var admin = accountRepo.findByUsername("admin");
            if (admin.isPresent()) {
                logger.info("Found admin account: " + admin.get().getUsername());
            } else {
                logger.info("Admin account not found");
            }
            
            logger.info("Basic operations test passed");
            
        } catch (Exception e) {
            logger.severe("Basic operations test failed: " + e.getMessage());
            throw e;
        }
    }
}
