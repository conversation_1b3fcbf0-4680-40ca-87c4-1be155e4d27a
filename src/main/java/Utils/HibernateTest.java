package Utils;

import Repository.AccountRepository;
import Repository.ComputerRepository;
import DTO.Account;
import DTO.Computer;

import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

/**
 * Test class để kiểm tra Hibernate configuration và repositories
 * Chỉ sử dụng để test, không dùng trong production
 */
public class HibernateTest {
    private static final Logger logger = Logger.getLogger(HibernateTest.class.getName());
    
    public static void main(String[] args) {
        try {
            logger.info("Starting Hibernate Test...");
            
            // Initialize Hibernate
            HibernateServiceProvider.init();
            
            // Run tests
            testAccountRepository();
            testComputerRepository();
            testTransactions();
            
            // Health check
            boolean healthy = HibernateServiceProvider.getInstance().healthCheck();
            logger.info("Health check result: " + (healthy ? "PASSED" : "FAILED"));
            
            // Log statistics
            HibernateServiceProvider.getInstance().logStatistics();
            
            logger.info("Hibernate Test completed successfully");
            
        } catch (Exception e) {
            logger.severe("Hibernate Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Cleanup
            HibernateServiceProvider.getInstance().shutdown();
        }
    }
    
    private static void testAccountRepository() {
        logger.info("=== Testing Account Repository ===");
        
        AccountRepository accountRepo = HibernateServiceProvider.getInstance()
                .getRepository(AccountRepository.class);
        
        try {
            // Test count
            long count = accountRepo.count();
            logger.info("Total accounts: " + count);
            
            // Test find all
            List<Account> accounts = accountRepo.findAllActive();
            logger.info("Active accounts: " + accounts.size());
            
            // Test find by username
            Optional<Account> admin = accountRepo.findByUsername("admin");
            if (admin.isPresent()) {
                logger.info("Found admin account: " + admin.get().getUsername());
            } else {
                logger.info("Admin account not found");
            }
            
            // Test find by role
            List<Account> admins = accountRepo.findByRole(Account.Role.ADMIN);
            logger.info("Admin accounts: " + admins.size());
            
            List<Account> users = accountRepo.findByRole(Account.Role.USER);
            logger.info("User accounts: " + users.size());
            
            // Test create new account
            if (!accountRepo.existsByUsername("testuser")) {
                Account testAccount = Account.builder()
                        .username("testuser")
                        .password(PasswordUtils.hashPassword("test123"))
                        .role(Account.Role.USER)
                        .balance(10000.0)
                        .build();
                
                Account saved = accountRepo.save(testAccount);
                logger.info("Created test account with ID: " + saved.getId());
                
                // Test update balance
                accountRepo.addBalance(saved.getId(), 5000.0);
                logger.info("Added balance to test account");
                
                // Test find by ID
                Optional<Account> found = accountRepo.findById(saved.getId());
                if (found.isPresent()) {
                    logger.info("Test account balance: " + found.get().getBalance());
                }
            }
            
            logger.info("Account Repository tests passed");
            
        } catch (Exception e) {
            logger.severe("Account Repository test failed: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testComputerRepository() {
        logger.info("=== Testing Computer Repository ===");
        
        ComputerRepository computerRepo = HibernateServiceProvider.getInstance()
                .getRepository(ComputerRepository.class);
        
        try {
            // Test count
            long count = computerRepo.count();
            logger.info("Total computers: " + count);
            
            // Test find all
            List<Computer> computers = computerRepo.findAllActive();
            logger.info("Active computers: " + computers.size());
            
            // Test find by status
            List<Computer> availableComputers = computerRepo.findAvailableComputers();
            logger.info("Available computers: " + availableComputers.size());
            
            List<Computer> inUseComputers = computerRepo.findComputersInUse();
            logger.info("Computers in use: " + inUseComputers.size());
            
            // Test find by type
            List<Computer> normalComputers = computerRepo.findByType(Computer.ComputerType.Normal);
            logger.info("Normal computers: " + normalComputers.size());
            
            List<Computer> vipComputers = computerRepo.findByType(Computer.ComputerType.Vip);
            logger.info("VIP computers: " + vipComputers.size());
            
            // Test create new computer
            if (!computerRepo.existsByName("TEST-PC")) {
                Computer testComputer = Computer.builder()
                        .name("TEST-PC")
                        .price(6000.0)
                        .type(Computer.ComputerType.Normal)
                        .status(Computer.ComputerStatus.OFF)
                        .build();
                
                Computer saved = computerRepo.save(testComputer);
                logger.info("Created test computer with ID: " + saved.getId());
                
                // Test update status
                computerRepo.updateStatus(saved.getId(), Computer.ComputerStatus.USING);
                logger.info("Updated test computer status");
                
                // Test find by ID
                Optional<Computer> found = computerRepo.findById(saved.getId());
                if (found.isPresent()) {
                    logger.info("Test computer status: " + found.get().getStatus());
                }
            }
            
            // Test statistics
            List<Object[]> statusStats = computerRepo.getStatusStatistics();
            logger.info("Status statistics:");
            for (Object[] stat : statusStats) {
                logger.info("  " + stat[0] + ": " + stat[1]);
            }
            
            List<Object[]> typeStats = computerRepo.getTypeStatistics();
            logger.info("Type statistics:");
            for (Object[] stat : typeStats) {
                logger.info("  " + stat[0] + ": " + stat[1] + " computers, avg price: " + stat[2]);
            }
            
            logger.info("Computer Repository tests passed");
            
        } catch (Exception e) {
            logger.severe("Computer Repository test failed: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testTransactions() {
        logger.info("=== Testing Transactions ===");
        
        AccountRepository accountRepo = HibernateServiceProvider.getInstance()
                .getRepository(AccountRepository.class);
        
        try {
            // Test transaction rollback
            Optional<Account> testAccount = accountRepo.findByUsername("testuser");
            if (testAccount.isPresent()) {
                double originalBalance = testAccount.get().getBalance();
                logger.info("Original balance: " + originalBalance);
                
                // Test successful deduction
                boolean success = accountRepo.deductBalance(testAccount.get().getId(), 1000.0);
                logger.info("Deduction success: " + success);
                
                // Test failed deduction (insufficient balance)
                boolean failed = accountRepo.deductBalance(testAccount.get().getId(), 999999.0);
                logger.info("Large deduction failed as expected: " + !failed);
                
                // Verify balance
                Optional<Account> updated = accountRepo.findById(testAccount.get().getId());
                if (updated.isPresent()) {
                    logger.info("Final balance: " + updated.get().getBalance());
                }
            }
            
            logger.info("Transaction tests passed");
            
        } catch (Exception e) {
            logger.severe("Transaction test failed: " + e.getMessage());
            throw e;
        }
    }
}
