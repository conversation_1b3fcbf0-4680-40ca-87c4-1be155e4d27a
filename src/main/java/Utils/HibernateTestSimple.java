package Utils;

/**
 * Test rất đơn giản để kiểm tra Hibernate có hoạt động không
 */
public class HibernateTestSimple {
    
    public static void main(String[] args) {
        System.out.println("=== Hibernate Simple Test ===");
        
        try {
            // Test 1: Khởi tạo HibernateUtil
            System.out.println("1. Testing HibernateUtil initialization...");
            var sessionFactory = HibernateUtil.getSessionFactory();
            System.out.println("   ✓ SessionFactory created: " + (sessionFactory != null));
            
            // Test 2: Mở session
            System.out.println("2. Testing session creation...");
            var session = HibernateUtil.getSessionFactory().openSession();
            System.out.println("   ✓ Session opened: " + (session != null));
            session.close();
            System.out.println("   ✓ Session closed");
            
            // Test 3: Khởi tạo Service Provider
            System.out.println("3. Testing Service Provider...");
            HibernateServiceProvider.init();
            System.out.println("   ✓ Service Provider initialized");
            
            // Test 4: Lấy repository
            System.out.println("4. Testing Repository access...");
            var accountRepo = HibernateServiceProvider.getInstance()
                    .getRepository(Repository.AccountRepository.class);
            System.out.println("   ✓ AccountRepository obtained: " + (accountRepo != null));
            
            System.out.println("\n=== All tests passed! ===");
            
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                HibernateServiceProvider.getInstance().shutdown();
                System.out.println("✓ Cleanup completed");
            } catch (Exception e) {
                System.err.println("Warning: Cleanup failed: " + e.getMessage());
            }
        }
    }
}
