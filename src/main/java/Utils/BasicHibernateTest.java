package Utils;

import DTO.Account;
import DTO.Computer;

/**
 * Test cơ bản nhất để kiểm tra entities có compile đ<PERSON>ợc không
 */
public class BasicHibernateTest {
    
    public static void main(String[] args) {
        System.out.println("=== Basic Hibernate Test ===");
        
        try {
            // Test 1: Tạo Account object
            System.out.println("1. Testing Account creation...");
            Account account = Account.builder()
                    .username("test")
                    .password("test123")
                    .role(Account.Role.USER)
                    .balance(1000.0)
                    .build();
            System.out.println("   ✓ Account created: " + account.getUsername());
            
            // Test 2: Tạo Computer object
            System.out.println("2. Testing Computer creation...");
            Computer computer = Computer.builder()
                    .name("PC-01")
                    .price(5000.0)
                    .type(Computer.ComputerType.Normal)
                    .status(Computer.ComputerStatus.OFF)
                    .build();
            System.out.println("   ✓ Computer created: " + computer.getName());
            
            // Test 3: Test enum methods
            System.out.println("3. Testing enum methods...");
            System.out.println("   Account role: " + account.getRole().toString());
            System.out.println("   Computer type: " + computer.getType().toString());
            System.out.println("   Computer status: " + computer.getStatus().toString());
            
            System.out.println("\n=== Basic test passed! ===");
            System.out.println("Entities can be created successfully.");
            
        } catch (Exception e) {
            System.err.println("❌ Basic test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
