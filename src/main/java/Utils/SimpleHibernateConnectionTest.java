package Utils;

import DTO.Account;
import DTO.Computer;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.cfg.Configuration;

import java.util.Date;

/**
 * Test đơn giản cho Hibernate connection và basic operations
 */
public class SimpleHibernateConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Hibernate Connection ===");
        
        SessionFactory sessionFactory = null;
        Session session = null;
        Transaction transaction = null;
        
        try {
            // Test 1: Tạo SessionFactory
            System.out.println("1. Creating Hibernate SessionFactory...");
            Configuration configuration = new Configuration();
            configuration.configure("hibernate.cfg.xml");
            sessionFactory = configuration.buildSessionFactory();
            System.out.println("   ✓ SessionFactory created successfully");
            
            // Test 2: Mở session
            System.out.println("2. Opening Hibernate session...");
            session = sessionFactory.openSession();
            System.out.println("   ✓ Session opened successfully");
            
            // Test 3: Bắt đầu transaction
            System.out.println("3. Starting transaction...");
            transaction = session.beginTransaction();
            System.out.println("   ✓ Transaction started");
            
            // Test 4: Tạo và lưu Account (nếu database sẵn sàng)
            System.out.println("4. Testing Account entity save...");
            Account testAccount = Account.builder()
                    .username("hibernate_test_" + System.currentTimeMillis())
                    .password("test123")
                    .role(Account.Role.USER)
                    .balance(500.0)
                    .createdAt(new Date())
                    .build();
            
            session.persist(testAccount);
            System.out.println("   ✓ Account entity saved with ID: " + testAccount.getId());
            
            // Test 5: Tạo và lưu Computer (nếu database sẵn sàng)
            System.out.println("5. Testing Computer entity save...");
            Computer testComputer = Computer.builder()
                    .name("TEST-PC-" + System.currentTimeMillis())
                    .price(3000.0)
                    .type(Computer.ComputerType.Normal)
                    .status(Computer.ComputerStatus.OFF)
                    .createdAt(new Date())
                    .build();
            
            session.persist(testComputer);
            System.out.println("   ✓ Computer entity saved with ID: " + testComputer.getId());
            
            // Test 6: Commit transaction
            System.out.println("6. Committing transaction...");
            transaction.commit();
            System.out.println("   ✓ Transaction committed successfully");
            
            // Test 7: Query test
            System.out.println("7. Testing simple query...");
            Long accountCount = session.createQuery("SELECT COUNT(a) FROM Account a", Long.class).getSingleResult();
            Long computerCount = session.createQuery("SELECT COUNT(c) FROM Computer c", Long.class).getSingleResult();
            System.out.println("   ✓ Total accounts in database: " + accountCount);
            System.out.println("   ✓ Total computers in database: " + computerCount);
            
            System.out.println("\n=== All Hibernate tests passed! ===");
            System.out.println("Hibernate is configured correctly and ready to use.");
            
        } catch (Exception e) {
            System.err.println("❌ Hibernate test failed: " + e.getMessage());
            e.printStackTrace();
            
            if (transaction != null && transaction.isActive()) {
                try {
                    transaction.rollback();
                    System.out.println("Transaction rolled back");
                } catch (Exception rollbackEx) {
                    System.err.println("Failed to rollback transaction: " + rollbackEx.getMessage());
                }
            }
        } finally {
            // Cleanup
            if (session != null) {
                try {
                    session.close();
                    System.out.println("Session closed");
                } catch (Exception e) {
                    System.err.println("Failed to close session: " + e.getMessage());
                }
            }
            
            if (sessionFactory != null) {
                try {
                    sessionFactory.close();
                    System.out.println("SessionFactory closed");
                } catch (Exception e) {
                    System.err.println("Failed to close SessionFactory: " + e.getMessage());
                }
            }
        }
    }
}
