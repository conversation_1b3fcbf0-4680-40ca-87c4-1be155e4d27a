package Utils;

import DTO.Account;
import DTO.Computer;
import java.util.Date;

/**
 * Test đơn giản cho Hibernate entities
 */
public class HibernateEntityTest {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Hibernate Entity Configuration ===");
        
        try {
            // Test 1: Tạo Account object với JPA annotations
            System.out.println("1. Testing Account entity...");
            Account account = Account.builder()
                    .username("testuser")
                    .password("testpass")
                    .role(Account.Role.USER)
                    .balance(1000.0)
                    .createdAt(new Date())
                    .build();
            System.out.println("   ✓ Account created: " + account.getUsername());
            System.out.println("   ✓ Role: " + account.getRole());
            System.out.println("   ✓ Balance: " + account.getBalance());
            
            // Test 2: Tạo Computer object với JPA annotations
            System.out.println("2. Testing Computer entity...");
            Computer computer = Computer.builder()
                    .name("PC-01")
                    .price(5000.0)
                    .type(Computer.ComputerType.Normal)
                    .status(Computer.ComputerStatus.OFF)
                    .createdAt(new Date())
                    .build();
            System.out.println("   ✓ Computer created: " + computer.getName());
            System.out.println("   ✓ Type: " + computer.getType());
            System.out.println("   ✓ Status: " + computer.getStatus());
            System.out.println("   ✓ Price: " + computer.getPrice());
            
            // Test 3: Test enum values
            System.out.println("3. Testing enum values...");
            System.out.println("   ✓ Account roles: ");
            for (Account.Role role : Account.Role.values()) {
                System.out.println("     - " + role + " (ordinal: " + role.ordinal() + ")");
            }
            
            System.out.println("   ✓ Computer types: ");
            for (Computer.ComputerType type : Computer.ComputerType.values()) {
                System.out.println("     - " + type + " (ordinal: " + type.ordinal() + ")");
            }
            
            System.out.println("   ✓ Computer statuses: ");
            for (Computer.ComputerStatus status : Computer.ComputerStatus.values()) {
                System.out.println("     - " + status + " (ordinal: " + status.ordinal() + ")");
            }
            
            System.out.println("\n=== All entity tests passed! ===");
            System.out.println("Entities are ready for Hibernate mapping.");
            
        } catch (Exception e) {
            System.err.println("❌ Entity test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
