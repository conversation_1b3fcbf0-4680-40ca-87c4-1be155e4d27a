package Utils;

import DTO.Account;
import DTO.Computer;
import DTO.Employee;
import DTO.Session;

/**
 * Test nhanh để kiểm tra compilation của các entities chính
 */
public class QuickCompileTest {
    
    public static void main(String[] args) {
        System.out.println("=== Quick Compile Test ===");
        
        try {
            // Test Account
            System.out.println("1. Testing Account...");
            Account account = new Account();
            account.setUsername("test");
            account.setPassword("test123");
            account.setRole(Account.Role.USER);
            account.setBalance(1000.0);
            System.out.println("   ✓ Account: " + account.getUsername());
            
            // Test Computer
            System.out.println("2. Testing Computer...");
            Computer computer = new Computer();
            computer.setName("PC-01");
            computer.setPrice(5000.0);
            computer.setType(Computer.ComputerType.Normal);
            computer.setStatus(Computer.ComputerStatus.OFF);
            System.out.println("   ✓ Computer: " + computer.getName());
            
            // Test Session
            System.out.println("3. Testing Session...");
            Session session = new Session();
            session.setUsedTime(3600);
            session.setUsedCost(5000.0);
            System.out.println("   ✓ Session: " + session.getUsedTime() + " seconds");
            
            // Test Employee
            System.out.println("4. Testing Employee...");
            Employee employee = new Employee();
            employee.setName("John Doe");
            employee.setSalaryPerHour(50000);
            System.out.println("   ✓ Employee: " + employee.getName());
            
            // Test relationships
            System.out.println("5. Testing relationships...");
            account.setCurrentSession(session);
            account.setEmployee(employee);
            System.out.println("   ✓ Relationships set");
            
            System.out.println("\n=== All entities compile successfully! ===");
            
        } catch (Exception e) {
            System.err.println("❌ Compilation failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
