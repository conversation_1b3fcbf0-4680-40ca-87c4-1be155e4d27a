package Utils;

import DAO.DBHelper;
import Repository.AccountRepository;
import Repository.ComputerRepository;
import DTO.Account;
import DTO.Computer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Helper class để migrate dữ liệu từ JDBC sang Hibernate
 * Chỉ sử dụng một lần khi chuyển đổi
 */
public class HibernateMigrationHelper {
    private static final Logger logger = Logger.getLogger(HibernateMigrationHelper.class.getName());
    
    private final AccountRepository accountRepository;
    private final ComputerRepository computerRepository;
    
    public HibernateMigrationHelper() {
        this.accountRepository = HibernateServiceProvider.getInstance().getRepository(AccountRepository.class);
        this.computerRepository = HibernateServiceProvider.getInstance().getRepository(ComputerRepository.class);
    }
    
    /**
     * <PERSON><PERSON><PERSON> tra và migrate dữ liệu nếu cần thiết
     */
    public void checkAndMigrate() {
        try {
            logger.info("Starting migration check...");
            
            // Kiểm tra xem đã có dữ liệu trong Hibernate chưa
            if (isHibernateDataExists()) {
                logger.info("Hibernate data already exists, skipping migration");
                return;
            }
            
            // Kiểm tra xem có dữ liệu JDBC cũ không
            if (!isLegacyDataExists()) {
                logger.info("No legacy data found, creating sample data");
                createSampleData();
                return;
            }
            
            // Thực hiện migration
            logger.info("Starting data migration from JDBC to Hibernate...");
            migrateAccounts();
            migrateComputers();
            // TODO: Migrate other entities
            
            logger.info("Data migration completed successfully");
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Migration failed", e);
            throw new RuntimeException("Migration failed", e);
        }
    }
    
    /**
     * Kiểm tra xem đã có dữ liệu Hibernate chưa
     */
    private boolean isHibernateDataExists() {
        try {
            long accountCount = accountRepository.count();
            long computerCount = computerRepository.count();
            
            return accountCount > 0 || computerCount > 0;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Error checking Hibernate data", e);
            return false;
        }
    }
    
    /**
     * Kiểm tra xem có dữ liệu JDBC cũ không
     */
    private boolean isLegacyDataExists() {
        try (Connection conn = DBHelper.getConnection()) {
            // Kiểm tra bảng account
            try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM account")) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next() && rs.getInt(1) > 0) {
                    return true;
                }
            }
            
            // Kiểm tra bảng computer
            try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM computer")) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next() && rs.getInt(1) > 0) {
                    return true;
                }
            }
            
            return false;
        } catch (SQLException e) {
            logger.log(Level.WARNING, "Error checking legacy data", e);
            return false;
        }
    }
    
    /**
     * Migrate accounts từ JDBC sang Hibernate
     */
    private void migrateAccounts() {
        try (Connection conn = DBHelper.getConnection()) {
            String sql = "SELECT * FROM account WHERE deletedAt IS NULL";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                ResultSet rs = stmt.executeQuery();
                
                int migratedCount = 0;
                while (rs.next()) {
                    Account account = Account.builder()
                            .id(rs.getInt("id"))
                            .username(rs.getString("username"))
                            .password(rs.getString("password"))
                            .balance(rs.getDouble("balance"))
                            .createdAt(rs.getTimestamp("createdAt"))
                            .deletedAt(rs.getTimestamp("deletedAt"))
                            .build();
                    
                    // Set role
                    account.setRole(rs.getInt("role"));
                    
                    // Save using Hibernate
                    accountRepository.save(account);
                    migratedCount++;
                }
                
                logger.info("Migrated " + migratedCount + " accounts");
            }
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error migrating accounts", e);
            throw new RuntimeException("Account migration failed", e);
        }
    }
    
    /**
     * Migrate computers từ JDBC sang Hibernate
     */
    private void migrateComputers() {
        try (Connection conn = DBHelper.getConnection()) {
            String sql = "SELECT * FROM computer WHERE deletedAt IS NULL";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                ResultSet rs = stmt.executeQuery();
                
                int migratedCount = 0;
                while (rs.next()) {
                    Computer computer = Computer.builder()
                            .id(rs.getInt("id"))
                            .name(rs.getString("name"))
                            .price(rs.getDouble("price"))
                            .createdAt(rs.getTimestamp("createdAt"))
                            .deletedAt(rs.getTimestamp("deletedAt"))
                            .build();
                    
                    // Set type and status
                    computer.setType(rs.getInt("type"));
                    computer.setStatus(rs.getInt("status"));
                    
                    // Save using Hibernate
                    computerRepository.save(computer);
                    migratedCount++;
                }
                
                logger.info("Migrated " + migratedCount + " computers");
            }
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error migrating computers", e);
            throw new RuntimeException("Computer migration failed", e);
        }
    }
    
    /**
     * Tạo dữ liệu mẫu nếu không có dữ liệu
     */
    private void createSampleData() {
        logger.info("Creating sample data...");
        
        // Tạo admin account
        Account admin = Account.builder()
                .username("admin")
                .password(PasswordUtils.hashPassword("admin123"))
                .role(Account.Role.ADMIN)
                .balance(0.0)
                .build();
        accountRepository.save(admin);
        
        // Tạo user account
        Account user = Account.builder()
                .username("user1")
                .password(PasswordUtils.hashPassword("user123"))
                .role(Account.Role.USER)
                .balance(50000.0)
                .build();
        accountRepository.save(user);
        
        // Tạo computers
        Computer computer1 = Computer.builder()
                .name("PC-01")
                .price(5000.0)
                .type(Computer.ComputerType.Normal)
                .status(Computer.ComputerStatus.OFF)
                .build();
        computerRepository.save(computer1);
        
        Computer computer2 = Computer.builder()
                .name("PC-VIP-01")
                .price(8000.0)
                .type(Computer.ComputerType.Vip)
                .status(Computer.ComputerStatus.OFF)
                .build();
        computerRepository.save(computer2);
        
        logger.info("Sample data created successfully");
    }
    
    /**
     * Verify migration results
     */
    public void verifyMigration() {
        logger.info("=== Migration Verification ===");
        
        long accountCount = accountRepository.count();
        long computerCount = computerRepository.count();
        
        logger.info("Total accounts: " + accountCount);
        logger.info("Total computers: " + computerCount);
        
        // Test some queries
        List<Account> admins = accountRepository.findByRole(Account.Role.ADMIN);
        logger.info("Admin accounts: " + admins.size());
        
        List<Computer> availableComputers = computerRepository.findAvailableComputers();
        logger.info("Available computers: " + availableComputers.size());
        
        logger.info("Migration verification completed");
    }
}
