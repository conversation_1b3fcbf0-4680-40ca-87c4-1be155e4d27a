package Utils;

import DTO.Account;
import DTO.Computer;
import Repository.AccountRepository;
import Repository.ComputerRepository;

/**
 * Test để kiểm tra compilation
 */
public class CompilationTest {
    
    public static void main(String[] args) {
        System.out.println("=== Compilation Test ===");
        
        try {
            // Test 1: Entities
            System.out.println("1. Testing entities...");
            Account account = new Account();
            Computer computer = new Computer();
            System.out.println("   ✓ Entities created");
            
            // Test 2: Repositories
            System.out.println("2. Testing repositories...");
            AccountRepository accountRepo = new AccountRepository();
            ComputerRepository computerRepo = new ComputerRepository();
            System.out.println("   ✓ Repositories created");
            
            // Test 3: Service Provider
            System.out.println("3. Testing service provider...");
            HibernateServiceProvider provider = HibernateServiceProvider.getInstance();
            System.out.println("   ✓ Service provider created");
            
            System.out.println("\n=== Compilation test passed! ===");
            System.out.println("All classes compile successfully.");
            
        } catch (Exception e) {
            System.err.println("❌ Compilation test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
