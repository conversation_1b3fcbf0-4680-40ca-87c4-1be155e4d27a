# Hibernate Setup Guide cho Cyber-CF

## 📋 Tổng quan
Đã tích hợp Hibernate ORM vào project Cyber-CF với cấu hình đơn giản và an toàn.

## 🔧 Cấu hình đã thêm

### 1. Dependencies (pom.xml)
```xml
<!-- Hibernate ORM với Jakarta EE -->
<dependency>
    <groupId>org.hibernate.orm</groupId>
    <artifactId>hibernate-core</artifactId>
    <version>6.4.4.Final</version>
</dependency>
<dependency>
    <groupId>jakarta.persistence</groupId>
    <artifactId>jakarta.persistence-api</artifactId>
    <version>3.1.0</version>
</dependency>
```

### 2. Hibernate Configuration (hibernate.cfg.xml)
- Database: MySQL localhost:3306/internet
- Username: root / Password: 1234
- Connection pool: 10 connections
- SQL logging: enabled (cho debug)

### 3. Core Classes
- **HibernateUtil**: SessionFactory management
- **BaseRepository**: CRUD operations c<PERSON> bản
- **AccountRepository**: Account-specific operations
- **ComputerRepository**: Computer-specific operations
- **HibernateServiceProvider**: Repository lifecycle management

## 🚀 Cách sử dụng

### 1. Khởi tạo Hibernate
```java
// Trong Main.java
HibernateServiceProvider.init();
```

### 2. Sử dụng Repository
```java
// Lấy repository
AccountRepository accountRepo = HibernateServiceProvider.getInstance()
    .getRepository(AccountRepository.class);

// CRUD operations
Optional<Account> account = accountRepo.findByUsername("admin");
List<Account> accounts = accountRepo.findAll();
Account newAccount = accountRepo.save(account);
```

### 3. Tạo Account mới
```java
Account account = Account.builder()
    .username("testuser")
    .password("hashedPassword")
    .role(Account.Role.USER)
    .balance(10000.0)
    .build();

Account saved = accountRepo.save(account);
```

## 🧪 Testing

### 1. Chạy test đơn giản
```bash
java -cp target/classes Utils.HibernateTestSimple
```

### 2. Chạy test đầy đủ
```bash
java -cp target/classes Utils.SimpleHibernateTest
```

## 🔍 Troubleshooting

### Lỗi thường gặp:

1. **ClassNotFoundException**: Thiếu dependencies
   - Kiểm tra pom.xml đã có đủ dependencies
   - Chạy `mvn clean compile`

2. **Connection refused**: Database không chạy
   - Khởi động MySQL server
   - Kiểm tra database "internet" đã tồn tại

3. **Table doesn't exist**: 
   - Hibernate sẽ tự tạo table với `hbm2ddl.auto=update`
   - Hoặc tạo table thủ công

### Debug tips:
- Bật SQL logging trong hibernate.cfg.xml
- Kiểm tra log trong console
- Sử dụng HibernateTestSimple để test cơ bản

## 📁 File Structure
```
src/main/java/
├── Utils/
│   ├── HibernateUtil.java
│   ├── HibernateServiceProvider.java
│   ├── HibernateTestSimple.java
│   └── SimpleHibernateTest.java
├── Repository/
│   ├── BaseRepository.java
│   ├── AccountRepository.java
│   └── ComputerRepository.java
└── DTO/
    ├── Account.java (updated)
    └── Computer.java (updated)

src/main/resources/
└── hibernate.cfg.xml
```

## ⚡ Performance Tips
- Sử dụng connection pooling (đã cấu hình)
- Đóng session sau mỗi operation
- Sử dụng batch operations cho bulk insert
- Cache queries nếu cần

## 🔒 Security Features
- Connection pooling với HikariCP
- Automatic transaction rollback
- Resource cleanup tự động
- Parameterized queries (tránh SQL injection)

## 🔄 Migration từ JDBC
- Hibernate hoạt động song song với JDBC hiện tại
- Có thể migrate từng module một cách từ từ
- Fallback về JDBC nếu Hibernate fail

## 📞 Support
Nếu gặp vấn đề, kiểm tra:
1. Database connection
2. Hibernate configuration
3. Entity mapping
4. Repository initialization
