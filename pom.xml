<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.example</groupId>
  <artifactId>main</artifactId>
  <version>1.0-SNAPSHOT</version>

  <properties>
    <!-- <PERSON><PERSON><PERSON><PERSON> lên Java 24 -->
    <maven.compiler.source>24</maven.compiler.source>
    <maven.compiler.target>24</maven.compiler.target>
    <java.version>24</java.version>
    <!-- <PERSON><PERSON><PERSON> bản <PERSON> hỗ trợ JDK 24 -->
    <lombok.version>1.18.38</lombok.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
      </resource>
    </resources>

    <plugins>
      <!-- Compiler plugin với annotation-processor paths -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.14.0</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <compilerArgs>--enable-preview</compilerArgs>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <!-- JDBC & UI libs -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.0.32</version>
    </dependency>
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <version>11.2.3.jre17</version>
    </dependency>
    <dependency>
      <groupId>com.toedter</groupId>
      <artifactId>jcalendar</artifactId>
      <version>1.4</version>
    </dependency>
    <dependency>
      <groupId>org.swinglabs</groupId>
      <artifactId>swingx</artifactId>
      <version>1.6.1</version>
    </dependency>
    <dependency>
      <groupId>org.swinglabs.swingx</groupId>
      <artifactId>swingx-core</artifactId>
      <version>1.6.5-1</version>
    </dependency>

    <!-- FlatLaf theme -->
    <dependency>
      <groupId>com.formdev</groupId>
      <artifactId>flatlaf</artifactId>
      <version>3.0</version>
    </dependency>
    <dependency>
      <groupId>com.formdev</groupId>
      <artifactId>flatlaf-extras</artifactId>
      <version>3.0</version>
    </dependency>
    <dependency>
      <groupId>com.formdev</groupId>
      <artifactId>flatlaf-intellij-themes</artifactId>
      <version>3.0</version>
    </dependency>

    <!-- Batik SVG support -->
    <dependency>
      <groupId>org.apache.xmlgraphics</groupId>
      <artifactId>batik-svg-dom</artifactId>
      <version>1.16</version>
    </dependency>
    <dependency>
      <groupId>org.apache.xmlgraphics</groupId>
      <artifactId>batik-transcoder</artifactId>
      <version>1.16</version>
    </dependency>

    <!-- Lombok (provided) -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- Hibernate ORM -->
    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>6.4.4.Final</version>
    </dependency>

    <!-- Jakarta Persistence API -->
    <dependency>
      <groupId>jakarta.persistence</groupId>
      <artifactId>jakarta.persistence-api</artifactId>
      <version>3.1.0</version>
    </dependency>

    <!-- JUnit 5 -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.9.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
